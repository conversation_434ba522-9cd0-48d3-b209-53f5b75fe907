/**
 * AddDiscForm Component for Disc Golf Inventory Management System
 *
 * A complete form for adding new discs to the collection with all required fields,
 * real-time validation feedback, manufacturer and plastic type suggestions,
 * flight numbers input with validation, and form submission handling.
 */

"use client";

import * as React from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormFieldGroup } from "./FormField";
import { useInventory } from "@/hooks/useInventory";
import { CreateDiscSchema } from "@/lib/validation";
import { DiscCondition, Location, FLIGHT_NUMBER_RANGES, DISC_WEIGHT_RANGE, type Disc } from "@/lib/types";
import { DISC_MANUFACTURERS, getPlasticTypesForManufacturer, DISC_COLORS } from "@/lib/discData";
import { formatConditionText, formatLocationText } from "@/lib/discUtils";
import { cn } from "@/lib/utils";
import { Plus, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import type { CreateDiscInput } from "@/hooks/useInventory";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Form data type based on CreateDiscInput
 */
type AddDiscFormData = CreateDiscInput;

/**
 * Props for the AddDiscForm component
 */
export interface AddDiscFormProps {
  /** Callback when disc is successfully added */
  onSuccess?: (disc: Disc) => void;
  /** Callback when form is cancelled */
  onCancel?: () => void;
  /** Callback when form submission starts */
  onSubmit?: () => void;
  /** Callback when form submission fails */
  onError?: (error: Error) => void;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the form in a card wrapper */
  showCard?: boolean;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * AddDiscForm component with comprehensive validation and suggestions
 *
 * Features:
 * - React Hook Form with Zod validation
 * - Real-time validation feedback
 * - Manufacturer and plastic type autocomplete
 * - Flight numbers validation with range checking
 * - Form submission with error handling
 * - Accessible form design
 * - Responsive layout
 *
 * @param props - AddDiscForm component props
 * @returns JSX element
 */
export function AddDiscForm({ onSuccess, onCancel, onSubmit, onError, className, showCard = true }: AddDiscFormProps) {
  const { addDisc, loading } = useInventory();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitError, setSubmitError] = React.useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = React.useState(false);

  // Form setup with React Hook Form and Zod validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    trigger,
    formState: { errors, isValid, isDirty },
  } = useForm<AddDiscFormData>({
    resolver: zodResolver(CreateDiscSchema),
    mode: "onChange",
    defaultValues: {
      manufacturer: "",
      mold: "",
      plasticType: "",
      weight: 175,
      condition: DiscCondition.NEW,
      flightNumbers: {
        speed: 5,
        glide: 5,
        turn: 0,
        fade: 2,
      },
      color: "",
      notes: "",
      currentLocation: Location.BAG,
      imageUrl: "",
    },
  });

  // Watch manufacturer to update plastic type suggestions
  const selectedManufacturer = watch("manufacturer");

  // Get plastic types for selected manufacturer
  const plasticTypes = React.useMemo(() => {
    return selectedManufacturer ? getPlasticTypesForManufacturer(selectedManufacturer) : [];
  }, [selectedManufacturer]);

  // Reset plastic type when manufacturer changes
  React.useEffect(() => {
    if (selectedManufacturer) {
      setValue("plasticType", "");
    }
  }, [selectedManufacturer, setValue]);

  /**
   * Handle form submission
   */
  const handleFormSubmit = async (data: AddDiscFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      setSubmitSuccess(false);

      // Call onSubmit callback to notify parent component
      onSubmit?.();

      // Convert string dates to Date objects if provided
      const processedData: AddDiscFormData = {
        ...data,
        purchaseDate: data.purchaseDate ? new Date(data.purchaseDate) : undefined,
        imageUrl: data.imageUrl?.trim() || undefined,
        notes: data.notes?.trim() || undefined,
      };

      const result = await addDisc(processedData);

      if (result.success && result.data) {
        setSubmitSuccess(true);
        reset();
        onSuccess?.(result.data);

        // Auto-hide success message after 3 seconds
        setTimeout(() => setSubmitSuccess(false), 3000);
      } else {
        const error = new Error(result.error?.message || "Failed to add disc");
        setSubmitError(error.message);
        onError?.(error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setSubmitError(errorMessage);
      onError?.(new Error(errorMessage));
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle form reset
   */
  const handleReset = () => {
    reset();
    setSubmitError(null);
    setSubmitSuccess(false);
  };

  /**
   * Custom form submit handler that ensures validation is triggered
   */
  const handleFormSubmitWithValidation = async (e: React.FormEvent) => {
    e.preventDefault();

    // Manually trigger validation for all fields
    const isFormValid = await trigger();

    if (isFormValid) {
      // If validation passes, call the normal handleSubmit
      handleSubmit(handleFormSubmit)(e);
    }
    // If validation fails, errors will be displayed automatically
  };

  const formContent = (
    <form onSubmit={handleFormSubmitWithValidation} className="space-y-6" role="form">
      {/* Basic Information */}
      <FormFieldGroup title="Basic Information" description="Enter the basic details about your disc">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Manufacturer */}
          <Controller
            name="manufacturer"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label id="manufacturer-label" htmlFor="manufacturer" className="text-sm font-medium">
                  Manufacturer *
                </label>
                <Select
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    // Trigger validation manually if needed
                    field.onBlur();
                  }}
                >
                  <SelectTrigger
                    id="manufacturer"
                    aria-labelledby="manufacturer-label"
                    className={cn(errors.manufacturer && "border-destructive focus-visible:ring-destructive/20")}
                  >
                    <SelectValue placeholder="Select manufacturer" />
                  </SelectTrigger>
                  <SelectContent>
                    {DISC_MANUFACTURERS.map((manufacturer) => (
                      <SelectItem key={manufacturer} value={manufacturer}>
                        {manufacturer}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.manufacturer && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="size-4" />
                    <span>{errors.manufacturer.message}</span>
                  </div>
                )}
              </div>
            )}
          />

          {/* Mold */}
          <Controller
            name="mold"
            control={control}
            render={({ field }) => (
              <FormField
                id="mold"
                label="Mold"
                placeholder="e.g., Destroyer, Buzzz, Judge"
                required
                error={errors.mold?.message}
                {...field}
              />
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Plastic Type */}
          <Controller
            name="plasticType"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label id="plasticType-label" htmlFor="plasticType" className="text-sm font-medium">
                  Plastic Type *
                </label>
                <Select value={field.value} onValueChange={field.onChange} disabled={!selectedManufacturer}>
                  <SelectTrigger
                    id="plasticType"
                    aria-labelledby="plasticType-label"
                    className={cn(errors.plasticType && "border-destructive focus-visible:ring-destructive/20")}
                  >
                    <SelectValue
                      placeholder={selectedManufacturer ? "Select plastic type" : "Select manufacturer first"}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {plasticTypes.map((plastic) => (
                      <SelectItem key={plastic} value={plastic}>
                        {plastic}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.plasticType && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="size-4" />
                    <span>{errors.plasticType.message}</span>
                  </div>
                )}
              </div>
            )}
          />

          {/* Weight */}
          <Controller
            name="weight"
            control={control}
            render={({ field }) => (
              <FormField
                id="weight"
                label="Weight (grams)"
                type="number"
                placeholder="175"
                required
                error={errors.weight?.message}
                helpText={`Range: ${DISC_WEIGHT_RANGE.min}-${DISC_WEIGHT_RANGE.max}g`}
                inputProps={{
                  min: DISC_WEIGHT_RANGE.min,
                  max: DISC_WEIGHT_RANGE.max,
                  step: 1,
                }}
                {...field}
                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
              />
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Color */}
          <Controller
            name="color"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label htmlFor="color" className="text-sm font-medium">
                  Color *
                </label>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={cn(errors.color && "border-destructive focus-visible:ring-destructive/20")}>
                    <SelectValue placeholder="Select color" />
                  </SelectTrigger>
                  <SelectContent>
                    {DISC_COLORS.map((color) => (
                      <SelectItem key={color} value={color}>
                        {color}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.color && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="size-4" />
                    <span>{errors.color.message}</span>
                  </div>
                )}
              </div>
            )}
          />

          {/* Condition */}
          <Controller
            name="condition"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label id="condition-label" htmlFor="condition" className="text-sm font-medium">
                  Condition *
                </label>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger id="condition" aria-labelledby="condition-label">
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(DiscCondition).map((condition) => (
                      <SelectItem key={condition} value={condition}>
                        {formatConditionText(condition)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.condition && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="size-4" />
                    <span>{errors.condition.message}</span>
                  </div>
                )}
              </div>
            )}
          />
        </div>
      </FormFieldGroup>

      {/* Flight Numbers */}
      <FormFieldGroup title="Flight Numbers" description="Enter the disc's flight characteristics">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* Speed */}
          <Controller
            name="flightNumbers.speed"
            control={control}
            render={({ field }) => (
              <FormField
                id="speed"
                label="Speed"
                type="number"
                required
                error={errors.flightNumbers?.speed?.message}
                helpText={`${FLIGHT_NUMBER_RANGES.speed.min}-${FLIGHT_NUMBER_RANGES.speed.max}`}
                inputProps={{
                  min: FLIGHT_NUMBER_RANGES.speed.min,
                  max: FLIGHT_NUMBER_RANGES.speed.max,
                  step: 1,
                }}
                {...field}
                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
              />
            )}
          />

          {/* Glide */}
          <Controller
            name="flightNumbers.glide"
            control={control}
            render={({ field }) => (
              <FormField
                id="glide"
                label="Glide"
                type="number"
                required
                error={errors.flightNumbers?.glide?.message}
                helpText={`${FLIGHT_NUMBER_RANGES.glide.min}-${FLIGHT_NUMBER_RANGES.glide.max}`}
                inputProps={{
                  min: FLIGHT_NUMBER_RANGES.glide.min,
                  max: FLIGHT_NUMBER_RANGES.glide.max,
                  step: 1,
                }}
                {...field}
                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
              />
            )}
          />

          {/* Turn */}
          <Controller
            name="flightNumbers.turn"
            control={control}
            render={({ field }) => (
              <FormField
                id="turn"
                label="Turn"
                type="number"
                required
                error={errors.flightNumbers?.turn?.message}
                helpText={`${FLIGHT_NUMBER_RANGES.turn.min} to ${FLIGHT_NUMBER_RANGES.turn.max}`}
                inputProps={{
                  min: FLIGHT_NUMBER_RANGES.turn.min,
                  max: FLIGHT_NUMBER_RANGES.turn.max,
                  step: 1,
                }}
                {...field}
                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
              />
            )}
          />

          {/* Fade */}
          <Controller
            name="flightNumbers.fade"
            control={control}
            render={({ field }) => (
              <FormField
                id="fade"
                label="Fade"
                type="number"
                required
                error={errors.flightNumbers?.fade?.message}
                helpText={`${FLIGHT_NUMBER_RANGES.fade.min}-${FLIGHT_NUMBER_RANGES.fade.max}`}
                inputProps={{
                  min: FLIGHT_NUMBER_RANGES.fade.min,
                  max: FLIGHT_NUMBER_RANGES.fade.max,
                  step: 1,
                }}
                {...field}
                onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
              />
            )}
          />
        </div>
      </FormFieldGroup>

      {/* Additional Information */}
      <FormFieldGroup title="Additional Information" description="Optional details about your disc">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Purchase Date */}
          <Controller
            name="purchaseDate"
            control={control}
            render={({ field }) => (
              <FormField
                id="purchaseDate"
                label="Purchase Date"
                type="date"
                error={errors.purchaseDate?.message}
                {...field}
                value={field.value ? new Date(field.value).toISOString().split("T")[0] : ""}
              />
            )}
          />

          {/* Purchase Price */}
          <Controller
            name="purchasePrice"
            control={control}
            render={({ field }) => (
              <FormField
                id="purchasePrice"
                label="Purchase Price ($)"
                type="number"
                placeholder="0.00"
                error={errors.purchasePrice?.message}
                inputProps={{
                  min: 0,
                  max: 1000,
                  step: 0.01,
                }}
                {...field}
                onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
              />
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Current Location */}
          <Controller
            name="currentLocation"
            control={control}
            render={({ field }) => (
              <div className="space-y-2">
                <label id="currentLocation-label" htmlFor="currentLocation" className="text-sm font-medium">
                  Current Location *
                </label>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger id="currentLocation" aria-labelledby="currentLocation-label">
                    <SelectValue placeholder="Select location" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(Location).map((location) => (
                      <SelectItem key={location} value={location}>
                        {formatLocationText(location)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.currentLocation && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="size-4" />
                    <span>{errors.currentLocation.message}</span>
                  </div>
                )}
              </div>
            )}
          />

          {/* Image URL */}
          <Controller
            name="imageUrl"
            control={control}
            render={({ field }) => (
              <FormField
                id="imageUrl"
                label="Image URL"
                type="url"
                placeholder="https://example.com/disc-image.jpg"
                error={errors.imageUrl?.message}
                {...field}
              />
            )}
          />
        </div>

        {/* Notes */}
        <Controller
          name="notes"
          control={control}
          render={({ field }) => (
            <div className="space-y-2">
              <label htmlFor="notes" className="text-sm font-medium">
                Notes
              </label>
              <Textarea
                id="notes"
                placeholder="Any additional notes about this disc..."
                className={cn("min-h-[100px]", errors.notes && "border-destructive focus-visible:ring-destructive/20")}
                {...field}
              />
              {errors.notes && (
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle className="size-4" />
                  <span>{errors.notes.message}</span>
                </div>
              )}
              <div className="text-xs text-muted-foreground">{field.value?.length || 0}/500 characters</div>
            </div>
          )}
        />
      </FormFieldGroup>

      {/* Submit Messages */}
      {submitError && (
        <div className="flex items-center gap-2 p-4 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
          <AlertCircle className="size-4 shrink-0" />
          <span>{submitError}</span>
        </div>
      )}

      {submitSuccess && (
        <div className="flex items-center gap-2 p-4 text-sm text-green-800 bg-green-100 border border-green-200 rounded-md dark:text-green-300 dark:bg-green-900/30 dark:border-green-800">
          <CheckCircle className="size-4 shrink-0" />
          <span>Disc added successfully!</span>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4">
        <Button type="submit" disabled={!isValid || isSubmitting || loading} className="flex-1 sm:flex-none">
          {isSubmitting ? (
            <>
              <Loader2 className="size-4 animate-spin" />
              Adding Disc...
            </>
          ) : (
            <>
              <Plus className="size-4" />
              Add Disc
            </>
          )}
        </Button>

        <Button
          type="button"
          variant="outline"
          onClick={handleReset}
          disabled={!isDirty || isSubmitting}
          className="flex-1 sm:flex-none"
        >
          Reset Form
        </Button>

        {onCancel && (
          <Button
            type="button"
            variant="ghost"
            onClick={onCancel}
            disabled={isSubmitting}
            className="flex-1 sm:flex-none"
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  );

  if (showCard) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Add New Disc</CardTitle>
        </CardHeader>
        <CardContent>{formContent}</CardContent>
      </Card>
    );
  }

  return <div className={className}>{formContent}</div>;
}
