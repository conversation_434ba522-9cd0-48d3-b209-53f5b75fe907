/**
 * OptimizedImage Component for Disc Golf Inventory Management System
 *
 * A wrapper around Next.js Image component with enhanced optimization features,
 * error handling, loading states, and accessibility improvements.
 */

"use client";

import * as React from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ImageIcon, AlertCircle } from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface OptimizedImageProps {
  /** Image source URL */
  src: string;
  /** Alt text for accessibility */
  alt: string;
  /** Image width (required for static images) */
  width?: number;
  /** Image height (required for static images) */
  height?: number;
  /** Whether to fill the container */
  fill?: boolean;
  /** Priority loading for above-the-fold images */
  priority?: boolean;
  /** CSS classes for the image */
  className?: string;
  /** CSS classes for the container */
  containerClassName?: string;
  /** Sizes attribute for responsive images */
  sizes?: string;
  /** Fallback component when image fails to load */
  fallback?: React.ReactNode;
  /** Loading placeholder component */
  placeholder?: React.ReactNode;
  /** Whether to show loading state */
  showLoading?: boolean;
  /** Callback when image loads successfully */
  onLoad?: () => void;
  /** Callback when image fails to load */
  onError?: () => void;
  /** Object fit style */
  objectFit?: "contain" | "cover" | "fill" | "none" | "scale-down";
  /** Object position style */
  objectPosition?: string;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * OptimizedImage component with enhanced features
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  priority = false,
  className,
  containerClassName,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
  fallback,
  placeholder,
  showLoading = true,
  onLoad,
  onError,
  objectFit = "cover",
  objectPosition = "center",
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);

  // Handle image load
  const handleLoad = React.useCallback(() => {
    setIsLoading(false);
    onLoad?.();
  }, [onLoad]);

  // Handle image error
  const handleError = React.useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  }, [onError]);

  // Default fallback component
  const defaultFallback = (
    <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-muted to-muted/50">
      <div className="flex flex-col items-center gap-2 text-muted-foreground">
        <AlertCircle className="h-8 w-8" />
        <span className="text-sm">Failed to load image</span>
      </div>
    </div>
  );

  // Default placeholder component
  const defaultPlaceholder = (
    <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-muted to-muted/50">
      <div className="flex flex-col items-center gap-2 text-muted-foreground">
        <ImageIcon className="h-8 w-8 animate-pulse" />
        <span className="text-sm">Loading...</span>
      </div>
    </div>
  );

  // Show error state
  if (hasError) {
    return (
      <div className={cn("relative", containerClassName)}>
        {fallback || defaultFallback}
      </div>
    );
  }

  return (
    <div className={cn("relative", containerClassName)}>
      {/* Loading placeholder */}
      {isLoading && showLoading && (
        <div className="absolute inset-0 z-10">
          {placeholder || defaultPlaceholder}
        </div>
      )}

      {/* Optimized Image */}
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        sizes={sizes}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100",
          objectFit === "contain" && "object-contain",
          objectFit === "cover" && "object-cover",
          objectFit === "fill" && "object-fill",
          objectFit === "none" && "object-none",
          objectFit === "scale-down" && "object-scale-down",
          className
        )}
        style={{
          objectPosition,
        }}
        onLoad={handleLoad}
        onError={handleError}
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      />
    </div>
  );
}

// ============================================================================
// SPECIALIZED IMAGE COMPONENTS
// ============================================================================

/**
 * DiscImage component specifically for disc images
 */
export function DiscImage({
  src,
  alt,
  priority = false,
  className,
  ...props
}: Omit<OptimizedImageProps, "fill" | "sizes"> & { src?: string }) {
  if (!src) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-muted to-muted/50">
        <ImageIcon className="h-16 w-16 text-muted-foreground/50" aria-hidden="true" />
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      fill
      priority={priority}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      className={cn("object-cover transition-transform duration-200 group-hover:scale-105", className)}
      objectFit="cover"
      {...props}
    />
  );
}

/**
 * Avatar image component for user profiles
 */
export function AvatarImage({
  src,
  alt,
  size = 40,
  className,
  ...props
}: Omit<OptimizedImageProps, "fill" | "width" | "height"> & {
  size?: number;
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn("rounded-full", className)}
      sizes={`${size}px`}
      objectFit="cover"
      {...props}
    />
  );
}

export default OptimizedImage;
