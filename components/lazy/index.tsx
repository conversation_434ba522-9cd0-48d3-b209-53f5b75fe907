/**
 * Lazy-loaded Components for Performance Optimization
 *
 * This module provides dynamically imported components to reduce initial bundle size
 * and improve performance by loading components only when needed.
 */

import dynamic from "next/dynamic";
import React from "react";

// Loading component for lazy-loaded components
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-4">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

// Dashboard Components (lazy-loaded)
export const LazyStatsOverview = dynamic(
  () => import("@/components/dashboard/StatsOverview").then((mod) => ({ default: mod.StatsOverview })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

export const LazyDistributionChart = dynamic(
  () => import("@/components/dashboard/DistributionChart").then((mod) => ({ default: mod.DistributionChart })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

// Dialog Components (lazy-loaded)
export const LazyExportDialog = dynamic(
  () => import("@/components/inventory/ExportDialog").then((mod) => ({ default: mod.ExportDialog })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

export const LazyImportDialog = dynamic(
  () => import("@/components/inventory/ImportDialog").then((mod) => ({ default: mod.ImportDialog })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

// Form Components (lazy-loaded)
export const LazyAddDiscForm = dynamic(
  () => import("@/components/forms/AddDiscForm").then((mod) => ({ default: mod.AddDiscForm })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

// Search Components (lazy-loaded for complex filtering)
export const LazyFilterBar = dynamic(
  () => import("@/components/search/FilterBar").then((mod) => ({ default: mod.FilterBar })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

// Type exports for TypeScript support
export type LazyComponentProps<T = Record<string, unknown>> = T & {
  loading?: boolean;
};
