/**
 * DiscCard Component for Disc Golf Inventory Management System
 *
 * A card component for displaying disc information in grid layouts with
 * responsive design, hover/focus states, action buttons, image placeholder
 * handling, and keyboard navigation support.
 */

import * as React from "react";
import Image from "next/image";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DiscBadge } from "@/components/ui/DiscBadge";
import { FlightNumbersDisplay } from "@/components/ui/FlightNumbersDisplay";
import { cn } from "@/lib/utils";
import { formatDiscDisplayName } from "@/lib/discUtils";
import type { DiscCardProps } from "@/lib/types";
import { Edit, Trash2, Disc3 } from "lucide-react";

/**
 * DiscCard component for displaying disc information in grid layouts
 *
 * @example
 * ```tsx
 * <DiscCard
 *   disc={disc}
 *   onEdit={(disc) => console.log('Edit:', disc)}
 *   onDelete={(id) => console.log('Delete:', id)}
 *   onSelect={(disc) => console.log('Select:', disc)}
 *   isSelected={false}
 *   showActions={true}
 * />
 * ```
 */
function DiscCard({
  disc,
  onEdit,
  onDelete,
  onSelect,
  isSelected = false,
  showActions = true,
  priority = false,
  className,
  ...props
}: DiscCardProps & React.ComponentProps<typeof Card>) {
  const displayName = formatDiscDisplayName(disc);
  const [imageError, setImageError] = React.useState(false);

  // Handle card click for selection
  const handleCardClick = React.useCallback(
    (event: React.MouseEvent) => {
      // Don't trigger selection if clicking on action buttons
      if ((event.target as HTMLElement).closest("[data-action-button]")) {
        return;
      }
      onSelect?.(disc);
    },
    [disc, onSelect]
  );

  // Handle keyboard navigation
  const handleKeyDown = React.useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        onSelect?.(disc);
      }
    },
    [disc, onSelect]
  );

  return (
    <Card
      className={cn(
        // Base styles
        "group relative cursor-pointer transition-all duration-200",
        // Hover and focus states
        "hover:shadow-md hover:-translate-y-1",
        "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
        // Selection state
        isSelected && "ring-2 ring-primary ring-offset-2 bg-accent/50",
        // Responsive sizing
        "w-full max-w-sm mx-auto",
        className
      )}
      onClick={handleCardClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Disc card for ${displayName}`}
      aria-pressed={isSelected}
      {...props}
    >
      {/* Disc Image */}
      <div className="relative aspect-square w-full overflow-hidden rounded-t-xl bg-muted">
        {disc.imageUrl && !imageError ? (
          <Image
            src={disc.imageUrl}
            alt={`${displayName} disc`}
            fill
            priority={priority}
            className="object-cover transition-transform duration-200 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            onError={() => setImageError(true)}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-muted to-muted/50">
            <Disc3 className="h-16 w-16 text-muted-foreground/50" aria-hidden="true" />
          </div>
        )}

        {/* Condition Badge - positioned over image */}
        <div className="absolute top-2 left-2">
          <DiscBadge condition={disc.condition} size="sm" />
        </div>

        {/* Action buttons - positioned over image */}
        {showActions && (
          <div className="absolute top-2 right-2 flex gap-1 opacity-0 transition-opacity duration-200 group-hover:opacity-100 group-focus-within:opacity-100">
            {onEdit && (
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-background/80 backdrop-blur-sm hover:bg-background"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(disc);
                }}
                aria-label={`Edit ${displayName}`}
                data-action-button
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="icon"
                className="h-8 w-8 bg-destructive/80 backdrop-blur-sm hover:bg-destructive"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(disc.id);
                }}
                aria-label={`Delete ${displayName}`}
                data-action-button
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Card Content */}
      <CardHeader className="pb-2">
        <div className="space-y-1">
          <h3 className="font-semibold leading-none tracking-tight text-sm">{disc.manufacturer}</h3>
          <p className="text-lg font-bold text-foreground">{disc.mold}</p>
          <p className="text-sm text-muted-foreground">
            {disc.plasticType} • {disc.weight}g
          </p>
        </div>
      </CardHeader>

      <CardContent className="pt-0 pb-4">
        <div className="space-y-3">
          {/* Flight Numbers */}
          <FlightNumbersDisplay flightNumbers={disc.flightNumbers} size="sm" showLabels={false} showTooltips={true} />

          {/* Color indicator */}
          <div className="flex items-center gap-2">
            <div
              className="h-4 w-4 rounded-full border border-border"
              style={{ backgroundColor: disc.color }}
              aria-label={`Disc color: ${disc.color}`}
            />
            <span className="text-sm text-muted-foreground capitalize">{disc.color}</span>
          </div>

          {/* Notes preview (if available) */}
          {disc.notes && <p className="text-xs text-muted-foreground line-clamp-2">{disc.notes}</p>}
        </div>
      </CardContent>
    </Card>
  );
}

export { DiscCard };
