/**
 * ExportDialog Component for Disc Golf Inventory Management System
 *
 * A dialog component for exporting disc collection data with format selection,
 * filename customization, and export options.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, FileText, Table, AlertCircle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Disc } from "@/lib/types";
import { exportAndDownload, generateExportFilename, type ExportFormat, type ExportOptions } from "@/lib/exportImport";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface ExportDialogProps {
  discs: Disc[];
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

interface ExportState {
  format: ExportFormat;
  filename: string;
  includeMetadata: boolean;
  prettyFormat: boolean;
  isExporting: boolean;
  error: string | null;
  success: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * ExportDialog component for exporting disc collection data
 *
 * @example
 * ```tsx
 * <ExportDialog
 *   discs={discs}
 *   isOpen={showExportDialog}
 *   onClose={() => setShowExportDialog(false)}
 * />
 * ```
 */
export function ExportDialog({ discs, isOpen = true, onClose, className }: ExportDialogProps) {
  const [state, setState] = React.useState<ExportState>({
    format: "json",
    filename: generateExportFilename("json"),
    includeMetadata: true,
    prettyFormat: true,
    isExporting: false,
    error: null,
    success: false,
  });

  // Update filename when format changes
  React.useEffect(() => {
    setState((prev) => ({
      ...prev,
      filename: generateExportFilename(prev.format),
    }));
  }, [state.format]);

  const handleFormatChange = (value: unknown) => {
    const format = value as ExportFormat;
    setState((prev) => ({
      ...prev,
      format,
      filename: generateExportFilename(format),
    }));
  };

  const handleFilenameChange = (filename: string) => {
    setState((prev) => ({ ...prev, filename }));
  };

  const handleExport = async () => {
    setState((prev) => ({ ...prev, isExporting: true, error: null, success: false }));

    try {
      const options: ExportOptions = {
        format: state.format,
        filename: state.filename,
        includeMetadata: state.includeMetadata,
        prettyFormat: state.prettyFormat,
      };

      exportAndDownload(discs, options);

      setState((prev) => ({ ...prev, success: true }));

      // Auto-close after successful export
      setTimeout(() => {
        onClose?.();
      }, 1500);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Export failed",
      }));
    } finally {
      setState((prev) => ({ ...prev, isExporting: false }));
    }
  };

  const formatOptions = [
    {
      value: "json" as const,
      label: "JSON",
      description: "Complete data with metadata",
      icon: FileText,
    },
    {
      value: "csv" as const,
      label: "CSV",
      description: "Spreadsheet compatible format",
      icon: Table,
    },
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <Card className={cn("w-full max-w-md mx-4", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Collection
          </CardTitle>
          <CardDescription>Export your disc collection ({discs.length} discs) to a file</CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-2">
            <Label htmlFor="format">Export Format</Label>
            <Select value={state.format} onValueChange={handleFormatChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                {formatOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <option.icon className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">{option.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filename Input */}
          <div className="space-y-2">
            <Label htmlFor="filename">Filename</Label>
            <Input
              id="filename"
              value={state.filename}
              onChange={(e) => handleFilenameChange(e.target.value)}
              placeholder="Enter filename"
            />
          </div>

          {/* JSON Options */}
          {state.format === "json" && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeMetadata"
                  checked={state.includeMetadata}
                  onChange={(e) => setState((prev) => ({ ...prev, includeMetadata: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="includeMetadata" className="text-sm">
                  Include metadata (export date, version, etc.)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="prettyFormat"
                  checked={state.prettyFormat}
                  onChange={(e) => setState((prev) => ({ ...prev, prettyFormat: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="prettyFormat" className="text-sm">
                  Pretty format (readable JSON)
                </Label>
              </div>
            </div>
          )}

          {/* Status Messages */}
          {state.error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-700">{state.error}</span>
            </div>
          )}

          {state.success && (
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-700">Export completed successfully!</span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} disabled={state.isExporting} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={state.isExporting || discs.length === 0} className="flex-1">
              {state.isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
