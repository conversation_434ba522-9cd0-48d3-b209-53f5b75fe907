/**
 * FilterBar Component for Disc Golf Inventory Management System
 *
 * A comprehensive filter controls component for disc collection filtering
 * with manufacturer multi-select, condition filters, flight number sliders,
 * and active filter display.
 */

"use client";

import * as React from "react";
import { Filter, ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { DiscCondition, Location, FLIGHT_NUMBER_RANGES } from "@/lib/types";
import type { EnhancedFilterCriteria } from "@/lib/filterUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Props for the FilterBar component
 */
export interface FilterBarProps {
  /** Current filter criteria */
  filters: EnhancedFilterCriteria;
  /** Callback when filters change */
  onChange: (filters: EnhancedFilterCriteria) => void;
  /** Available options for filters */
  options: {
    manufacturers: string[];
    conditions: DiscCondition[];
    locations: Location[];
    colors: string[];
    plasticTypes: string[];
  };
  /** Whether the filter bar is collapsed */
  isCollapsed?: boolean;
  /** Callback when collapse state changes */
  onToggleCollapse?: (collapsed: boolean) => void;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the collapse button */
  showCollapseButton?: boolean;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get display name for condition
 */
function getConditionDisplayName(condition: DiscCondition): string {
  const names: Record<DiscCondition, string> = {
    [DiscCondition.NEW]: "New",
    [DiscCondition.GOOD]: "Good",
    [DiscCondition.FAIR]: "Fair",
    [DiscCondition.WORN]: "Worn",
    [DiscCondition.DAMAGED]: "Damaged",
  };
  return names[condition];
}

/**
 * Get display name for location
 */
function getLocationDisplayName(location: Location): string {
  const names: Record<Location, string> = {
    [Location.BAG]: "In Bag",
    [Location.CAR]: "In Car",
    [Location.HOME]: "At Home",
    [Location.LOANED]: "Loaned Out",
    [Location.LOST]: "Lost",
  };
  return names[location];
}

/**
 * Count active filters
 */
function countActiveFilters(filters: EnhancedFilterCriteria): number {
  let count = 0;

  if (filters.manufacturers?.length) count += filters.manufacturers.length;
  if (filters.conditions?.length) count += filters.conditions.length;
  if (filters.locations?.length) count += filters.locations.length;
  if (filters.colors?.length) count += filters.colors.length;
  if (filters.plasticTypes?.length) count += filters.plasticTypes.length;
  if (filters.speedRange) count++;
  if (filters.glideRange) count++;
  if (filters.turnRange) count++;
  if (filters.fadeRange) count++;
  if (filters.weightRange) count++;

  return count;
}

// ============================================================================
// SUB-COMPONENTS
// ============================================================================

/**
 * Multi-select checkbox group component
 */
interface MultiSelectProps<T extends string> {
  label: string;
  options: T[];
  selected: T[];
  onChange: (selected: T[]) => void;
  getDisplayName?: (value: T) => string;
  className?: string;
}

function MultiSelect<T extends string>({
  label,
  options,
  selected,
  onChange,
  getDisplayName = (value) => value,
  className,
}: MultiSelectProps<T>) {
  const handleToggle = (value: T) => {
    const newSelected = selected.includes(value) ? selected.filter((item) => item !== value) : [...selected, value];
    onChange(newSelected);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label className="text-sm font-medium">{label}</Label>
      <div className="space-y-1 max-h-32 overflow-y-auto">
        {options.map((option) => (
          <label
            key={option}
            className="flex items-center space-x-2 cursor-pointer hover:bg-accent/50 rounded px-2 py-1"
          >
            <input
              type="checkbox"
              checked={selected.includes(option)}
              onChange={() => handleToggle(option)}
              className="rounded border-input text-primary focus:ring-primary focus:ring-2"
            />
            <span className="text-sm">{getDisplayName(option)}</span>
          </label>
        ))}
      </div>
    </div>
  );
}

/**
 * Range slider component
 */
interface RangeSliderProps {
  label: string;
  min: number;
  max: number;
  value: { min: number; max: number } | undefined;
  onChange: (value: { min: number; max: number } | undefined) => void;
  step?: number;
  className?: string;
}

function RangeSlider({ label, min, max, value, onChange, step = 1, className }: RangeSliderProps) {
  const currentMin = value?.min ?? min;
  const currentMax = value?.max ?? max;

  const handleMinChange = (newMin: number) => {
    onChange({ min: newMin, max: Math.max(newMin, currentMax) });
  };

  const handleMaxChange = (newMax: number) => {
    onChange({ min: Math.min(currentMin, newMax), max: newMax });
  };

  const handleReset = () => {
    onChange(undefined);
  };

  const isActive = value !== undefined;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">{label}</Label>
        {isActive && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
          >
            Reset
          </Button>
        )}
      </div>
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <input
            type="range"
            min={min}
            max={max}
            step={step}
            value={currentMin}
            onChange={(e) => handleMinChange(Number(e.target.value))}
            className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <span className="text-xs text-muted-foreground w-8 text-center">{currentMin}</span>
        </div>
        <div className="flex items-center space-x-2">
          <input
            type="range"
            min={min}
            max={max}
            step={step}
            value={currentMax}
            onChange={(e) => handleMaxChange(Number(e.target.value))}
            className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <span className="text-xs text-muted-foreground w-8 text-center">{currentMax}</span>
        </div>
        <div className="text-xs text-muted-foreground text-center">
          Range: {currentMin} - {currentMax}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * FilterBar component for comprehensive disc filtering
 *
 * @example
 * ```tsx
 * <FilterBar
 *   filters={filters}
 *   onChange={setFilters}
 *   options={filterOptions}
 *   isCollapsed={isCollapsed}
 *   onToggleCollapse={setIsCollapsed}
 * />
 * ```
 */
export function FilterBar({
  filters,
  onChange,
  options,
  isCollapsed = false,
  onToggleCollapse,
  className,
  showCollapseButton = true,
}: FilterBarProps) {
  const activeFilterCount = countActiveFilters(filters);

  // Handle filter changes
  const handleFilterChange = (updates: Partial<EnhancedFilterCriteria>) => {
    onChange({ ...filters, ...updates });
  };

  // Clear all filters
  const handleClearAll = () => {
    onChange({});
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <CardTitle className="text-base">Filters</CardTitle>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {activeFilterCount > 0 && (
              <Button type="button" variant="ghost" size="sm" onClick={handleClearAll} className="text-xs">
                Clear All
              </Button>
            )}
            {showCollapseButton && onToggleCollapse && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => onToggleCollapse(!isCollapsed)}
                className="h-8 w-8"
              >
                {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="space-y-6">
          {/* Manufacturer Filter */}
          <MultiSelect
            label="Manufacturer"
            options={options.manufacturers}
            selected={filters.manufacturers || []}
            onChange={(manufacturers) => handleFilterChange({ manufacturers })}
          />

          {/* Condition Filter */}
          <MultiSelect
            label="Condition"
            options={options.conditions}
            selected={filters.conditions || []}
            onChange={(conditions) => handleFilterChange({ conditions })}
            getDisplayName={getConditionDisplayName}
          />

          {/* Location Filter */}
          <MultiSelect
            label="Location"
            options={options.locations}
            selected={filters.locations || []}
            onChange={(locations) => handleFilterChange({ locations })}
            getDisplayName={getLocationDisplayName}
          />

          {/* Flight Number Ranges */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">Flight Numbers</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <RangeSlider
                label="Speed"
                min={FLIGHT_NUMBER_RANGES.speed.min}
                max={FLIGHT_NUMBER_RANGES.speed.max}
                value={filters.speedRange}
                onChange={(speedRange) => handleFilterChange({ speedRange })}
              />
              <RangeSlider
                label="Glide"
                min={FLIGHT_NUMBER_RANGES.glide.min}
                max={FLIGHT_NUMBER_RANGES.glide.max}
                value={filters.glideRange}
                onChange={(glideRange) => handleFilterChange({ glideRange })}
              />
              <RangeSlider
                label="Turn"
                min={FLIGHT_NUMBER_RANGES.turn.min}
                max={FLIGHT_NUMBER_RANGES.turn.max}
                value={filters.turnRange}
                onChange={(turnRange) => handleFilterChange({ turnRange })}
              />
              <RangeSlider
                label="Fade"
                min={FLIGHT_NUMBER_RANGES.fade.min}
                max={FLIGHT_NUMBER_RANGES.fade.max}
                value={filters.fadeRange}
                onChange={(fadeRange) => handleFilterChange({ fadeRange })}
              />
            </div>
          </div>

          {/* Weight Range */}
          <RangeSlider
            label="Weight (grams)"
            min={150}
            max={180}
            value={filters.weightRange}
            onChange={(weightRange) => handleFilterChange({ weightRange })}
          />
        </CardContent>
      )}
    </Card>
  );
}

// ============================================================================
// EXPORTS
// ============================================================================

export default FilterBar;
