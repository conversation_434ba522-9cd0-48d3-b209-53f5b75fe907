/**
 * Web Vitals Monitoring Component for Disc Golf Inventory Management System
 *
 * This component tracks Core Web Vitals and performance metrics using the web-vitals library.
 * It provides real-time performance monitoring and can send metrics to analytics services.
 */

"use client";

import { useEffect, useState } from "react";
import { onCLS, onINP, onFCP, onLCP, onTTFB } from "web-vitals";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface Metric {
  name: string;
  value: number;
  rating: "good" | "needs-improvement" | "poor";
  delta: number;
  id: string;
}

interface WebVitalsConfig {
  /** Whether to log metrics to console */
  debug?: boolean;
  /** Whether to send metrics to analytics */
  sendToAnalytics?: boolean;
  /** Custom analytics endpoint */
  analyticsEndpoint?: string;
  /** Sample rate for metrics collection (0-1) */
  sampleRate?: number;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Send metric to analytics service
 */
function sendToAnalytics(metric: Metric, config: WebVitalsConfig): void {
  // Skip if analytics is disabled or sample rate check fails
  if (!config.sendToAnalytics || Math.random() > (config.sampleRate || 1)) {
    return;
  }

  // Log to console in debug mode
  if (config.debug) {
    console.log("Web Vital:", metric);
  }

  // Send to custom analytics endpoint if provided
  if (config.analyticsEndpoint) {
    fetch(config.analyticsEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        metric: metric.name,
        value: metric.value,
        rating: metric.rating,
        id: metric.id,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      }),
    }).catch((error) => {
      console.error("Failed to send web vital to analytics:", error);
    });
  }

  // Send to Google Analytics 4 if available
  if (typeof window !== "undefined" && (window as unknown as { gtag?: unknown }).gtag) {
    (window as unknown as { gtag: (event: string, name: string, params: Record<string, unknown>) => void }).gtag(
      "event",
      metric.name,
      {
        event_category: "Web Vitals",
        event_label: metric.id,
        value: Math.round(metric.name === "CLS" ? metric.value * 1000 : metric.value),
        custom_map: {
          metric_rating: metric.rating,
        },
      }
    );
  }
}

/**
 * Get performance rating based on metric thresholds
 */
function getPerformanceRating(name: string, value: number): "good" | "needs-improvement" | "poor" {
  const thresholds = {
    CLS: { good: 0.1, poor: 0.25 },
    INP: { good: 200, poor: 500 },
    FCP: { good: 1800, poor: 3000 },
    LCP: { good: 2500, poor: 4000 },
    TTFB: { good: 800, poor: 1800 },
  };

  const threshold = thresholds[name as keyof typeof thresholds];
  if (!threshold) return "good";

  if (value <= threshold.good) return "good";
  if (value <= threshold.poor) return "needs-improvement";
  return "poor";
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * Web Vitals monitoring component
 */
export function WebVitals({
  debug = process.env.NODE_ENV === "development",
  sendToAnalytics: enableAnalytics = process.env.NODE_ENV === "production",
  analyticsEndpoint,
  sampleRate = 1,
}: WebVitalsConfig = {}) {
  useEffect(() => {
    const config: WebVitalsConfig = {
      debug,
      sendToAnalytics: enableAnalytics,
      analyticsEndpoint,
      sampleRate,
    };

    // Track Core Web Vitals
    onCLS((metric) => {
      const enhancedMetric = {
        ...metric,
        rating: getPerformanceRating(metric.name, metric.value),
      };
      sendToAnalytics(enhancedMetric, config);
    });

    onINP((metric) => {
      const enhancedMetric = {
        ...metric,
        rating: getPerformanceRating(metric.name, metric.value),
      };
      sendToAnalytics(enhancedMetric, config);
    });

    onFCP((metric) => {
      const enhancedMetric = {
        ...metric,
        rating: getPerformanceRating(metric.name, metric.value),
      };
      sendToAnalytics(enhancedMetric, config);
    });

    onLCP((metric) => {
      const enhancedMetric = {
        ...metric,
        rating: getPerformanceRating(metric.name, metric.value),
      };
      sendToAnalytics(enhancedMetric, config);
    });

    onTTFB((metric) => {
      const enhancedMetric = {
        ...metric,
        rating: getPerformanceRating(metric.name, metric.value),
      };
      sendToAnalytics(enhancedMetric, config);
    });
  }, [debug, enableAnalytics, analyticsEndpoint, sampleRate]);

  // This component doesn't render anything
  return null;
}

// ============================================================================
// PERFORMANCE MONITORING HOOK
// ============================================================================

/**
 * Hook for accessing performance metrics
 */
export function useWebVitals() {
  const [metrics, setMetrics] = useState<Record<string, Metric>>({});

  useEffect(() => {
    const updateMetric = (metric: Metric) => {
      setMetrics((prev) => ({
        ...prev,
        [metric.name]: {
          ...metric,
          rating: getPerformanceRating(metric.name, metric.value),
        },
      }));
    };

    onCLS(updateMetric);
    onINP(updateMetric);
    onFCP(updateMetric);
    onLCP(updateMetric);
    onTTFB(updateMetric);
  }, []);

  return metrics;
}

export default WebVitals;
