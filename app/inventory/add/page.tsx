/**
 * Add Disc Page for Disc Golf Inventory Management System
 *
 * Dedicated page for adding new discs with full-screen form layout,
 * progress indication, success/error handling, and navigation back to inventory.
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LazyAddDiscForm } from "@/components/lazy";
import { ArrowLeft, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

type PageState = "form" | "submitting" | "success" | "error";

interface SuccessState {
  disc: Disc;
  message: string;
}

interface ErrorState {
  message: string;
  details?: string;
}

// ============================================================================
// PROGRESS INDICATOR COMPONENT
// ============================================================================

interface ProgressIndicatorProps {
  state: PageState;
  className?: string;
}

function ProgressIndicator({ state, className }: ProgressIndicatorProps) {
  const steps = [
    { key: "form", label: "Fill Details", icon: "1" },
    { key: "submitting", label: "Saving", icon: <Loader2 className="h-4 w-4 animate-spin" /> },
    { key: "success", label: "Complete", icon: <CheckCircle className="h-4 w-4" /> },
  ];

  const getCurrentStepIndex = () => {
    switch (state) {
      case "form":
        return 0;
      case "submitting":
        return 1;
      case "success":
      case "error":
        return 2;
      default:
        return 0;
    }
  };

  const currentStepIndex = getCurrentStepIndex();

  return (
    <div className={cn("flex items-center justify-center space-x-4", className)}>
      {steps.map((step, index) => (
        <div key={step.key} className="flex items-center">
          {/* Step Circle */}
          <div
            className={cn(
              "flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium transition-colors",
              index <= currentStepIndex
                ? "border-primary bg-primary text-primary-foreground"
                : "border-muted-foreground/30 bg-background text-muted-foreground",
              state === "error" &&
                index === currentStepIndex &&
                "border-destructive bg-destructive text-destructive-foreground"
            )}
          >
            {typeof step.icon === "string" ? step.icon : step.icon}
          </div>

          {/* Step Label */}
          <span
            className={cn(
              "ml-2 text-sm font-medium transition-colors",
              index <= currentStepIndex ? "text-foreground" : "text-muted-foreground"
            )}
          >
            {step.label}
          </span>

          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div
              className={cn(
                "w-12 h-0.5 mx-4 transition-colors",
                index < currentStepIndex ? "bg-primary" : "bg-muted-foreground/30"
              )}
            />
          )}
        </div>
      ))}
    </div>
  );
}

// ============================================================================
// SUCCESS STATE COMPONENT
// ============================================================================

interface SuccessStateProps {
  disc: Disc;
  message: string;
  onAddAnother: () => void;
  onViewInventory: () => void;
  onViewDisc: () => void;
}

function SuccessState({ disc, message, onAddAnother, onViewInventory, onViewDisc }: SuccessStateProps) {
  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <CheckCircle className="h-6 w-6 text-green-600" />
        </div>
        <CardTitle className="text-2xl">Disc Added Successfully!</CardTitle>
        <CardDescription>{message}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Disc Summary */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h3 className="font-semibold mb-2">Added Disc:</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-muted-foreground">Manufacturer:</span>
              <span className="ml-2 font-medium">{disc.manufacturer}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Mold:</span>
              <span className="ml-2 font-medium">{disc.mold}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Plastic:</span>
              <span className="ml-2 font-medium">{disc.plasticType}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Weight:</span>
              <span className="ml-2 font-medium">{disc.weight}g</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button onClick={onAddAnother} className="flex-1">
            Add Another Disc
          </Button>
          <Button variant="outline" onClick={onViewDisc} className="flex-1">
            View Disc Details
          </Button>
          <Button variant="outline" onClick={onViewInventory} className="flex-1">
            View Inventory
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// ============================================================================
// ERROR STATE COMPONENT
// ============================================================================

interface ErrorStateProps {
  message: string;
  details?: string;
  onRetry: () => void;
  onCancel: () => void;
}

function ErrorState({ message, details, onRetry, onCancel }: ErrorStateProps) {
  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <AlertCircle className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle className="text-2xl">Failed to Add Disc</CardTitle>
        <CardDescription>{message}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {details && (
          <div className="bg-muted/50 rounded-lg p-4">
            <h3 className="font-semibold mb-2">Error Details:</h3>
            <p className="text-sm text-muted-foreground">{details}</p>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3">
          <Button onClick={onRetry} className="flex-1">
            Try Again
          </Button>
          <Button variant="outline" onClick={onCancel} className="flex-1">
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// ============================================================================
// MAIN ADD DISC PAGE COMPONENT
// ============================================================================

export default function AddDiscPage() {
  const router = useRouter();

  // Page state
  const [pageState, setPageState] = React.useState<PageState>("form");
  const [successState, setSuccessState] = React.useState<SuccessState | null>(null);
  const [errorState, setErrorState] = React.useState<ErrorState | null>(null);

  // Handlers
  const handleSuccess = (disc: Disc) => {
    setPageState("success");
    setSuccessState({
      disc,
      message: `${disc.manufacturer} ${disc.mold} has been added to your collection.`,
    });
  };

  const handleError = (error: Error) => {
    setPageState("error");
    setErrorState({
      message: "There was a problem adding your disc to the collection.",
      details: error.message,
    });
  };

  const handleCancel = () => {
    router.push("/inventory");
  };

  const handleRetry = () => {
    setPageState("form");
    setErrorState(null);
  };

  const handleAddAnother = () => {
    setPageState("form");
    setSuccessState(null);
    setErrorState(null);
  };

  const handleViewInventory = () => {
    router.push("/inventory");
  };

  const handleViewDisc = () => {
    if (successState?.disc) {
      router.push(`/inventory/${successState.disc.id}`);
    }
  };

  const handleFormSubmit = () => {
    setPageState("submitting");
  };

  return (
    <Layout>
      <PageContainer
        title="Add New Disc"
        description="Add a disc to your collection"
        actions={
          <Button variant="outline" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Inventory
          </Button>
        }
      >
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Progress Indicator */}
          <ProgressIndicator state={pageState} />

          {/* Content based on state */}
          {pageState === "form" && (
            <LazyAddDiscForm
              onSuccess={handleSuccess}
              onCancel={handleCancel}
              onSubmit={handleFormSubmit}
              onError={handleError}
              showCard={true}
              className="max-w-2xl mx-auto"
            />
          )}

          {pageState === "submitting" && (
            <Card className="max-w-2xl mx-auto">
              <CardContent className="py-12">
                <div className="text-center space-y-4">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                  <h3 className="text-lg font-semibold">Adding disc to your collection...</h3>
                  <p className="text-muted-foreground">Please wait while we save your disc.</p>
                </div>
              </CardContent>
            </Card>
          )}

          {pageState === "success" && successState && (
            <SuccessState
              disc={successState.disc}
              message={successState.message}
              onAddAnother={handleAddAnother}
              onViewInventory={handleViewInventory}
              onViewDisc={handleViewDisc}
            />
          )}

          {pageState === "error" && errorState && (
            <ErrorState
              message={errorState.message}
              details={errorState.details}
              onRetry={handleRetry}
              onCancel={handleCancel}
            />
          )}
        </div>
      </PageContainer>
    </Layout>
  );
}
