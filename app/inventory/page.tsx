/**
 * Inventory Page for Disc Golf Inventory Management System
 *
 * Main inventory page with responsive grid layout, integrated search and filters,
 * pagination for large collections, and loading/error states.
 */

"use client";

import * as React from "react";
import { Suspense } from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer, Section } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { EmptyState } from "@/components/ui/EmptyState";
import { DiscCard } from "@/components/inventory";
import { SearchInput } from "@/components/search";
import { LazyFilterBar } from "@/components/lazy";
import { useInventory } from "@/hooks/useInventory";
import { useSearch } from "@/hooks/useSearch";
import { getFilterSuggestions } from "@/lib/filterUtils";
import { DiscCondition, Location } from "@/lib/types";
import type { Disc } from "@/lib/types";
import { Plus, Grid3X3, List, Filter } from "lucide-react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
}

type ViewMode = "grid" | "list";

// ============================================================================
// PAGINATION COMPONENT
// ============================================================================

interface PaginationProps {
  pagination: PaginationState;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
}

function Pagination({ pagination, onPageChange, onItemsPerPageChange }: PaginationProps) {
  const { currentPage, totalPages, itemsPerPage, totalItems } = pagination;

  if (totalPages <= 1) return null;

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 py-4">
      <div className="text-sm text-muted-foreground">
        Showing {startItem}-{endItem} of {totalItems} discs
      </div>

      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={() => onPageChange(currentPage - 1)} disabled={currentPage === 1}>
          Previous
        </Button>

        <div className="flex items-center gap-1">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = i + 1;
            return (
              <Button
                key={page}
                variant={page === currentPage ? "default" : "outline"}
                size="sm"
                onClick={() => onPageChange(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            );
          })}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Items per page:</span>
        <select
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          className="text-sm border rounded px-2 py-1"
        >
          <option value={12}>12</option>
          <option value={24}>24</option>
          <option value={48}>48</option>
          <option value={96}>96</option>
        </select>
      </div>
    </div>
  );
}

// ============================================================================
// MAIN INVENTORY PAGE COMPONENT
// ============================================================================

function InventoryPageContent() {
  const router = useRouter();
  const { discs, loading, error, removeDisc } = useInventory();

  // Search and filter state
  const { searchState, filteredDiscs, setQuery, setFilters, clearAll, hasActiveSearch, hasActiveFilters } = useSearch(
    discs,
    {
      debounceDelay: 300,
      syncWithURL: true,
      enableMetrics: true,
    }
  );

  // UI state
  const [viewMode, setViewMode] = React.useState<ViewMode>("grid");
  const [isFilterCollapsed, setIsFilterCollapsed] = React.useState(false);

  // Pagination state
  const [pagination, setPagination] = React.useState<PaginationState>({
    currentPage: 1,
    itemsPerPage: 24,
    totalItems: 0,
    totalPages: 0,
  });

  // Update pagination when filtered discs change
  React.useEffect(() => {
    const totalItems = filteredDiscs.length;
    const totalPages = Math.ceil(totalItems / pagination.itemsPerPage);

    setPagination((prev) => ({
      ...prev,
      totalItems,
      totalPages,
      currentPage: Math.min(prev.currentPage, totalPages || 1),
    }));
  }, [filteredDiscs.length, pagination.itemsPerPage]);

  // Get paginated discs
  const paginatedDiscs = React.useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.itemsPerPage;
    const endIndex = startIndex + pagination.itemsPerPage;
    return filteredDiscs.slice(startIndex, endIndex);
  }, [filteredDiscs, pagination.currentPage, pagination.itemsPerPage]);

  // Get filter options from the current disc collection
  const filterOptions = React.useMemo(() => {
    const suggestions = getFilterSuggestions(discs);
    return {
      manufacturers: suggestions.manufacturers,
      conditions: Object.values(DiscCondition),
      locations: Object.values(Location),
      colors: suggestions.colors,
      plasticTypes: suggestions.plasticTypes,
    };
  }, [discs]);

  // Handlers
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, currentPage: page }));
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setPagination((prev) => ({
      ...prev,
      itemsPerPage,
      currentPage: 1,
      totalPages: Math.ceil(prev.totalItems / itemsPerPage),
    }));
  };

  const handleEditDisc = (disc: Disc) => {
    router.push(`/inventory/edit/${disc.id}`);
  };

  const handleDeleteDisc = async (discId: string) => {
    if (confirm("Are you sure you want to delete this disc?")) {
      await removeDisc(discId);
    }
  };

  const handleAddDisc = () => {
    router.push("/inventory/add");
  };

  // Loading state
  if (loading) {
    return (
      <Layout>
        <PageContainer title="Disc Golf Inventory" description="Loading your disc collection...">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </PageContainer>
      </Layout>
    );
  }

  // Error state
  if (error) {
    return (
      <Layout>
        <PageContainer title="Disc Golf Inventory" description="Error loading your collection">
          <EmptyState
            variant="error"
            title="Failed to load inventory"
            description={error.message}
            onAction={() => window.location.reload()}
          />
        </PageContainer>
      </Layout>
    );
  }

  // Empty collection state
  if (discs.length === 0) {
    return (
      <Layout>
        <PageContainer
          title="Disc Golf Inventory"
          description="Start building your disc golf collection"
          actions={
            <Button onClick={handleAddDisc}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Disc
            </Button>
          }
        >
          <EmptyState
            variant="default"
            onAction={handleAddDisc}
            secondaryActionLabel="Import Collection"
            onSecondaryAction={() => router.push("/inventory/import")}
          />
        </PageContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer
        title="Disc Golf Inventory"
        description={`${discs.length} discs in your collection`}
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}>
              {viewMode === "grid" ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
            </Button>
            <Button onClick={handleAddDisc}>
              <Plus className="h-4 w-4 mr-2" />
              Add Disc
            </Button>
          </div>
        }
      >
        {/* Search and Filter Section */}
        <Section>
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <SearchInput
                  value={searchState.query}
                  onChange={setQuery}
                  placeholder="Search discs by name, manufacturer, mold..."
                  size="lg"
                  showSearchIcon={true}
                  showClearButton={true}
                />
              </div>
              <Button variant="outline" onClick={() => setIsFilterCollapsed(!isFilterCollapsed)} className="sm:w-auto">
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {hasActiveFilters && (
                  <span className="ml-2 bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs">
                    Active
                  </span>
                )}
              </Button>
            </div>

            {/* Filter Bar */}
            <LazyFilterBar
              filters={searchState.filters}
              onChange={setFilters}
              options={filterOptions}
              isCollapsed={isFilterCollapsed}
              onToggleCollapse={setIsFilterCollapsed}
            />

            {/* Active Search/Filter Indicator */}
            {(hasActiveSearch || hasActiveFilters) && (
              <div className="flex items-center justify-between bg-muted/50 rounded-lg p-3">
                <div className="text-sm text-muted-foreground">
                  {filteredDiscs.length} of {discs.length} discs match your criteria
                </div>
                <Button variant="ghost" size="sm" onClick={clearAll}>
                  Clear All
                </Button>
              </div>
            )}
          </div>
        </Section>

        {/* Results Section */}
        <Section>
          {filteredDiscs.length === 0 ? (
            <EmptyState
              variant="search"
              onAction={clearAll}
              secondaryActionLabel="Add New Disc"
              onSecondaryAction={handleAddDisc}
            />
          ) : (
            <>
              {/* Disc Grid */}
              <div
                className={cn(
                  "grid gap-6",
                  viewMode === "grid" ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" : "grid-cols-1"
                )}
              >
                {paginatedDiscs.map((disc, index) => (
                  <DiscCard
                    key={disc.id}
                    disc={disc}
                    onEdit={handleEditDisc}
                    onDelete={handleDeleteDisc}
                    showActions={true}
                    priority={index < 6} // Priority load for first 6 images (above-the-fold)
                    className={viewMode === "list" ? "flex-row" : ""}
                  />
                ))}
              </div>

              {/* Pagination */}
              <Pagination
                pagination={pagination}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            </>
          )}
        </Section>
      </PageContainer>
    </Layout>
  );
}

export default function InventoryPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <InventoryPageContent />
    </Suspense>
  );
}
