/**
 * Integration test for Add Disc Workflow
 *
 * Tests the complete user journey of adding a new disc to the inventory
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { AddDiscForm } from "@/components/forms/AddDiscForm";
import { DiscCondition, Location } from "@/lib/types";
import { setupTest, cleanupTest, testAccessibility, mockInventoryHook, createMockDisc } from "../utils/testUtils";

describe("Add Disc Workflow Integration", () => {
  beforeEach(() => {
    setupTest();
    mockInventoryHook.addDisc.mockClear();
  });
  afterEach(cleanupTest);

  describe("Complete Add Disc Journey", () => {
    it.skip("allows user to add a new disc with all required fields", async () => {
      // TODO: Fix Base UI Select component integration in tests
      const user = userEvent.setup();
      const mockDisc = createMockDisc({
        manufacturer: "Innova",
        mold: "Destroyer",
        plasticType: "Champion",
        weight: 175,
        condition: DiscCondition.NEW,
        color: "Blue",
        currentLocation: Location.BAG,
      });

      mockInventoryHook.addDisc.mockResolvedValue({
        success: true,
        data: mockDisc,
      });

      render(<AddDiscForm />);

      // Step 1: Fill in basic disc information
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");

      // Step 2: Set physical properties
      const weightInput = screen.getByLabelText(/weight/i);
      await user.clear(weightInput);
      await user.type(weightInput, "175");

      await user.selectOptions(screen.getByLabelText(/condition/i), DiscCondition.NEW);
      await user.type(screen.getByLabelText(/color/i), "Blue");

      // Step 3: Set flight numbers
      const speedInput = screen.getByLabelText(/speed/i);
      await user.clear(speedInput);
      await user.type(speedInput, "12");

      const glideInput = screen.getByLabelText(/glide/i);
      await user.clear(glideInput);
      await user.type(glideInput, "5");

      const turnInput = screen.getByLabelText(/turn/i);
      await user.clear(turnInput);
      await user.type(turnInput, "-1");

      const fadeInput = screen.getByLabelText(/fade/i);
      await user.clear(fadeInput);
      await user.type(fadeInput, "3");

      // Step 4: Set location and optional fields
      await user.selectOptions(screen.getByLabelText(/location/i), Location.BAG);
      await user.type(screen.getByLabelText(/notes/i), "Test disc for integration testing");

      // Step 5: Submit the form
      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      // Verify the submission
      await waitFor(() => {
        expect(mockInventoryHook.addDisc).toHaveBeenCalledTimes(1);
      });

      // Verify success message
      await waitFor(() => {
        expect(screen.getByText(/disc added successfully/i)).toBeInTheDocument();
      });
    });

    it.skip("handles validation errors gracefully", async () => {
      // TODO: Fix form validation after Base UI migration
      const user = userEvent.setup();

      render(<AddDiscForm />);

      // Try to submit without filling required fields
      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      // Check for validation errors
      await waitFor(() => {
        expect(screen.getByText(/manufacturer is required/i)).toBeInTheDocument();
        expect(screen.getByText(/mold is required/i)).toBeInTheDocument();
        expect(screen.getByText(/plastic type is required/i)).toBeInTheDocument();
        expect(screen.getByText(/color is required/i)).toBeInTheDocument();
      });

      // Verify form is still interactive
      expect(submitButton).toBeEnabled();
      expect(screen.getByLabelText(/manufacturer/i)).toBeEnabled();
    });

    it.skip("allows user to reset form and start over", async () => {
      // TODO: Fix Base UI Select component integration in tests
      const user = userEvent.setup();

      render(<AddDiscForm />);

      // Fill in some fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.type(screen.getByLabelText(/color/i), "Blue");

      // Verify fields are filled
      expect(screen.getByDisplayValue("Innova")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Destroyer")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Blue")).toBeInTheDocument();

      // Reset the form
      const resetButton = screen.getByRole("button", { name: /reset/i });
      await user.click(resetButton);

      // Verify fields are cleared
      expect(screen.getByLabelText(/manufacturer/i)).toHaveValue("");
      expect(screen.getByLabelText(/mold/i)).toHaveValue("");
      expect(screen.getByLabelText(/color/i)).toHaveValue("");
    });

    it.skip("handles server errors during submission", async () => {
      // TODO: Fix Base UI Select component integration in tests
      const user = userEvent.setup();

      mockInventoryHook.addDisc.mockResolvedValue({
        success: false,
        error: { message: "Server error: Failed to save disc" },
      });

      render(<AddDiscForm />);

      // Fill in required fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");
      await user.type(screen.getByLabelText(/color/i), "Blue");

      // Submit the form
      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      // Verify error handling
      await waitFor(() => {
        expect(screen.getByText(/server error: failed to save disc/i)).toBeInTheDocument();
      });

      // Verify form is still usable
      expect(submitButton).toBeEnabled();
    });

    it.skip("provides real-time validation feedback", async () => {
      // TODO: Fix form validation after Base UI migration
      const user = userEvent.setup();

      render(<AddDiscForm />);

      // Test weight validation
      const weightInput = screen.getByLabelText(/weight/i);
      await user.clear(weightInput);
      await user.type(weightInput, "50"); // Below minimum

      // Move focus away to trigger validation
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText(/weight must be between/i)).toBeInTheDocument();
      });

      // Fix the weight
      await user.clear(weightInput);
      await user.type(weightInput, "175");
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText(/weight must be between/i)).not.toBeInTheDocument();
      });
    });

    it.skip("supports keyboard navigation throughout the form", async () => {
      // TODO: Fix Base UI Select component integration in tests
      const user = userEvent.setup();

      render(<AddDiscForm />);

      // Start from manufacturer field
      const manufacturerSelect = screen.getByLabelText(/manufacturer/i);
      manufacturerSelect.focus();
      expect(manufacturerSelect).toHaveFocus();

      // Tab through form fields
      await user.tab();
      expect(screen.getByLabelText(/mold/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/plastic type/i)).toHaveFocus();

      // Continue tabbing to verify all fields are accessible
      await user.tab();
      expect(screen.getByLabelText(/weight/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/condition/i)).toHaveFocus();
    });

    it.skip("maintains form state during user interaction", async () => {
      // TODO: Fix Base UI Select component integration in tests
      const user = userEvent.setup();

      render(<AddDiscForm />);

      // Fill in multiple fields
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");
      await user.selectOptions(screen.getByLabelText(/plastic type/i), "Champion");

      // Navigate away and back
      await user.tab();
      await user.tab();

      // Verify state is maintained
      expect(screen.getByDisplayValue("Innova")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Destroyer")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Champion")).toBeInTheDocument();

      // Add more data
      await user.type(screen.getByLabelText(/color/i), "Blue");

      // Verify all state is still there
      expect(screen.getByDisplayValue("Innova")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Destroyer")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Champion")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Blue")).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it.skip("maintains accessibility throughout the workflow", async () => {
      // TODO: Fix Base UI Select component accessibility in tests
      const { container } = render(<AddDiscForm />);

      // Test initial accessibility
      await testAccessibility(container);

      // Fill in some fields and test again
      const user = userEvent.setup();
      await user.selectOptions(screen.getByLabelText(/manufacturer/i), "Innova");
      await user.type(screen.getByLabelText(/mold/i), "Destroyer");

      await testAccessibility(container);
    });

    it.skip("provides proper error announcements", async () => {
      // TODO: Fix Base UI Select component integration in tests
      const user = userEvent.setup();

      render(<AddDiscForm />);

      // Submit to trigger errors
      const submitButton = screen.getByRole("button", { name: /add disc/i });
      await user.click(submitButton);

      // Verify error messages are properly associated
      await waitFor(() => {
        const manufacturerInput = screen.getByLabelText(/manufacturer/i);
        const errorId = manufacturerInput.getAttribute("aria-describedby");
        expect(errorId).toBeTruthy();

        if (errorId) {
          const errorElement = document.getElementById(errorId.split(" ")[0]);
          expect(errorElement).toHaveTextContent(/manufacturer is required/i);
        }
      });
    });
  });
});
