/**
 * Integration test for Search and Filter Functionality
 *
 * Tests the complete user journey of searching and filtering discs
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { DiscCondition, Location } from "@/lib/types";
import { setupTest, cleanupTest, testAccessibility, mockDiscs, createMockDisc } from "../utils/testUtils";

// Mock a search and filter component (this would be the actual component in real implementation)
const MockInventoryWithSearch = ({ discs = mockDiscs }) => {
  const [searchTerm, setSearchTerm] = React.useState("");
  const [selectedManufacturers, setSelectedManufacturers] = React.useState<string[]>([]);
  const [selectedConditions, setSelectedConditions] = React.useState<DiscCondition[]>([]);
  const [selectedLocations, setSelectedLocations] = React.useState<Location[]>([]);

  const filteredDiscs = React.useMemo(() => {
    return discs.filter((disc) => {
      // Search term filter
      if (
        searchTerm &&
        !disc.mold.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !disc.manufacturer.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return false;
      }

      // Manufacturer filter
      if (selectedManufacturers.length > 0 && !selectedManufacturers.includes(disc.manufacturer)) {
        return false;
      }

      // Condition filter
      if (selectedConditions.length > 0 && !selectedConditions.includes(disc.condition)) {
        return false;
      }

      // Location filter
      if (selectedLocations.length > 0 && !selectedLocations.includes(disc.currentLocation)) {
        return false;
      }

      return true;
    });
  }, [discs, searchTerm, selectedManufacturers, selectedConditions, selectedLocations]);

  return (
    <div>
      {/* Search Input */}
      <input
        type="text"
        placeholder="Search discs..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        aria-label="Search discs"
      />

      {/* Manufacturer Filter */}
      <fieldset>
        <legend>Filter by Manufacturer</legend>
        {["Innova", "Discraft", "Dynamic Discs"].map((manufacturer) => (
          <label key={manufacturer}>
            <input
              type="checkbox"
              checked={selectedManufacturers.includes(manufacturer)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedManufacturers([...selectedManufacturers, manufacturer]);
                } else {
                  setSelectedManufacturers(selectedManufacturers.filter((m) => m !== manufacturer));
                }
              }}
            />
            {manufacturer}
          </label>
        ))}
      </fieldset>

      {/* Condition Filter */}
      <fieldset>
        <legend>Filter by Condition</legend>
        {Object.values(DiscCondition).map((condition) => (
          <label key={condition}>
            <input
              type="checkbox"
              checked={selectedConditions.includes(condition)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedConditions([...selectedConditions, condition]);
                } else {
                  setSelectedConditions(selectedConditions.filter((c) => c !== condition));
                }
              }}
            />
            {condition}
          </label>
        ))}
      </fieldset>

      {/* Location Filter */}
      <fieldset>
        <legend>Filter by Location</legend>
        {Object.values(Location).map((location) => (
          <label key={location}>
            <input
              type="checkbox"
              checked={selectedLocations.includes(location)}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedLocations([...selectedLocations, location]);
                } else {
                  setSelectedLocations(selectedLocations.filter((l) => l !== location));
                }
              }}
            />
            {location}
          </label>
        ))}
      </fieldset>

      {/* Results */}
      <div role="region" aria-label="Search results">
        <p>Found {filteredDiscs.length} discs</p>
        {filteredDiscs.length === 0 ? (
          <p>No discs found matching your criteria</p>
        ) : (
          <ul>
            {filteredDiscs.map((disc) => (
              <li key={disc.id}>
                {disc.manufacturer} {disc.mold} ({disc.condition}, {disc.currentLocation})
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Clear Filters Button */}
      <button
        onClick={() => {
          setSearchTerm("");
          setSelectedManufacturers([]);
          setSelectedConditions([]);
          setSelectedLocations([]);
        }}
      >
        Clear All Filters
      </button>
    </div>
  );
};

describe("Search and Filter Integration", () => {
  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Search Functionality", () => {
    it("allows users to search by disc mold", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Initial state - all discs shown
      expect(screen.getByText("Found 3 discs")).toBeInTheDocument();

      // Search for "Destroyer"
      const searchInput = screen.getByLabelText("Search discs");
      await user.type(searchInput, "Destroyer");

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText(/Innova Destroyer/)).toBeInTheDocument();
      });
    });

    it("allows users to search by manufacturer", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Search for "Discraft"
      const searchInput = screen.getByLabelText("Search discs");
      await user.type(searchInput, "Discraft");

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText(/Discraft Buzzz/)).toBeInTheDocument();
      });
    });

    it("shows no results for non-matching search", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Search for something that doesn't exist
      const searchInput = screen.getByLabelText("Search discs");
      await user.type(searchInput, "NonExistentDisc");

      await waitFor(() => {
        expect(screen.getByText("Found 0 discs")).toBeInTheDocument();
        expect(screen.getByText("No discs found matching your criteria")).toBeInTheDocument();
      });
    });

    it("is case insensitive", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Search with different case
      const searchInput = screen.getByLabelText("Search discs");
      await user.type(searchInput, "destroyer");

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText(/Innova Destroyer/)).toBeInTheDocument();
      });
    });
  });

  describe("Filter Functionality", () => {
    it("allows filtering by manufacturer", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Filter by Innova
      const innovaCheckbox = screen.getByLabelText("Innova");
      await user.click(innovaCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText(/Innova Destroyer/)).toBeInTheDocument();
      });
    });

    it("allows filtering by condition", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Filter by WORN condition
      const wornCheckbox = screen.getByLabelText(DiscCondition.WORN);
      await user.click(wornCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText(/Dynamic Discs Judge.*worn/)).toBeInTheDocument();
      });
    });

    it("allows filtering by location", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Filter by HOME location
      const homeCheckbox = screen.getByLabelText(Location.HOME);
      await user.click(homeCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText(/Dynamic Discs Judge.*home/)).toBeInTheDocument();
      });
    });

    it("supports multiple filter selections", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Filter by multiple manufacturers
      const innovaCheckbox = screen.getByLabelText("Innova");
      const discraftCheckbox = screen.getByLabelText("Discraft");

      await user.click(innovaCheckbox);
      await user.click(discraftCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 2 discs")).toBeInTheDocument();
        expect(screen.getByText(/Innova Destroyer/)).toBeInTheDocument();
        expect(screen.getByText(/Discraft Buzzz/)).toBeInTheDocument();
      });
    });
  });

  describe("Combined Search and Filter", () => {
    it("applies both search and filters together", async () => {
      const user = userEvent.setup();

      const customDiscs = [
        createMockDisc({ id: "1", manufacturer: "Innova", mold: "Destroyer", condition: DiscCondition.NEW }),
        createMockDisc({ id: "2", manufacturer: "Innova", mold: "Wraith", condition: DiscCondition.GOOD }),
        createMockDisc({ id: "3", manufacturer: "Discraft", mold: "Destroyer", condition: DiscCondition.NEW }),
      ];

      render(<MockInventoryWithSearch discs={customDiscs} />);

      // Search for "Destroyer"
      const searchInput = screen.getByLabelText("Search discs");
      await user.type(searchInput, "Destroyer");

      // Filter by Innova
      const innovaCheckbox = screen.getByLabelText("Innova");
      await user.click(innovaCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText(/Innova Destroyer/)).toBeInTheDocument();
        expect(screen.queryByText(/Discraft Destroyer/)).not.toBeInTheDocument();
      });
    });

    it("shows no results when search and filters don't match", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Search for "Destroyer"
      const searchInput = screen.getByLabelText("Search discs");
      await user.type(searchInput, "Destroyer");

      // Filter by Discraft (which doesn't make Destroyers in our mock data)
      const discraftCheckbox = screen.getByLabelText("Discraft");
      await user.click(discraftCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 0 discs")).toBeInTheDocument();
        expect(screen.getByText("No discs found matching your criteria")).toBeInTheDocument();
      });
    });
  });

  describe("Clear Filters", () => {
    it("clears all filters and search when clear button is clicked", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Apply search and filters
      const searchInput = screen.getByLabelText("Search discs");
      await user.type(searchInput, "Destroyer");

      const innovaCheckbox = screen.getByLabelText("Innova");
      await user.click(innovaCheckbox);

      const newCheckbox = screen.getByLabelText(DiscCondition.NEW);
      await user.click(newCheckbox);

      // Verify filters are applied
      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
      });

      // Clear all filters
      const clearButton = screen.getByText("Clear All Filters");
      await user.click(clearButton);

      // Verify everything is cleared
      await waitFor(() => {
        expect(screen.getByText("Found 3 discs")).toBeInTheDocument();
        expect(searchInput).toHaveValue("");
        expect(innovaCheckbox).not.toBeChecked();
        expect(newCheckbox).not.toBeChecked();
      });
    });
  });

  describe("Real-time Updates", () => {
    it("updates results immediately as user types", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      const searchInput = screen.getByLabelText("Search discs");

      // Type "D" - should show Destroyer, Discraft, and Dynamic Discs
      await user.type(searchInput, "D");
      await waitFor(() => {
        expect(screen.getByText("Found 3 discs")).toBeInTheDocument();
      });

      // Type "De" - should show only Destroyer
      await user.type(searchInput, "e");
      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
      });

      // Clear search - should show all discs
      await user.clear(searchInput);
      await waitFor(() => {
        expect(screen.getByText("Found 3 discs")).toBeInTheDocument();
      });
    });

    it("updates results immediately when filters change", async () => {
      const user = userEvent.setup();

      render(<MockInventoryWithSearch />);

      // Check Innova filter
      const innovaCheckbox = screen.getByLabelText("Innova");
      await user.click(innovaCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
      });

      // Uncheck Innova filter
      await user.click(innovaCheckbox);

      await waitFor(() => {
        expect(screen.getByText("Found 3 discs")).toBeInTheDocument();
      });
    });
  });

  describe("Accessibility", () => {
    it("maintains accessibility throughout search and filter operations", async () => {
      const { container } = render(<MockInventoryWithSearch />);

      // Test initial accessibility
      await testAccessibility(container);

      const user = userEvent.setup();

      // Apply some filters and test again
      await user.type(screen.getByLabelText("Search discs"), "Destroyer");
      await user.click(screen.getByLabelText("Innova"));

      await testAccessibility(container);
    });

    it("provides proper ARIA labels and descriptions", () => {
      render(<MockInventoryWithSearch />);

      // Check search input
      const searchInput = screen.getByLabelText("Search discs");
      expect(searchInput).toBeInTheDocument();

      // Check filter fieldsets
      expect(screen.getByRole("group", { name: "Filter by Manufacturer" })).toBeInTheDocument();
      expect(screen.getByRole("group", { name: "Filter by Condition" })).toBeInTheDocument();
      expect(screen.getByRole("group", { name: "Filter by Location" })).toBeInTheDocument();

      // Check results region
      expect(screen.getByRole("region", { name: "Search results" })).toBeInTheDocument();
    });
  });
});
