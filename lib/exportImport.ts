/**
 * Export/Import Functionality for the Disc Golf Inventory Management System
 *
 * This module provides comprehensive data export and import capabilities including
 * JSON export with full data, CSV export for spreadsheet compatibility, JSON import
 * with validation, and error handling for invalid imports.
 */

import type { Disc } from "./types";
import { DiscSchema } from "./validation";
import { exportDiscsToJson, exportDiscsToCSV, createImportSummary } from "./discUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Export format options
 */
export type ExportFormat = "json" | "csv";

/**
 * Export options configuration
 */
export interface ExportOptions {
  format: ExportFormat;
  filename?: string;
  includeMetadata?: boolean;
  prettyFormat?: boolean;
}

/**
 * Import result with detailed information
 */
export interface ImportResult {
  success: boolean;
  data?: Disc[];
  error?: string;
  summary?: ImportSummary;
  validationErrors?: ValidationError[];
}

/**
 * Import summary information
 */
export interface ImportSummary {
  totalImported: number;
  totalSkipped: number;
  totalErrors: number;
  newDiscs: number;
  duplicates: number;
  replacements: number;
}

/**
 * Validation error details
 */
export interface ValidationError {
  index: number;
  field?: string;
  message: string;
  value?: unknown;
}

/**
 * File download options
 */
export interface DownloadOptions {
  filename: string;
  mimeType: string;
}

// ============================================================================
// EXPORT FUNCTIONALITY
// ============================================================================

/**
 * Export disc collection with comprehensive options
 *
 * @param discs - Array of discs to export
 * @param options - Export configuration options
 * @returns Export data as string
 */
export function exportCollection(discs: Disc[], options: ExportOptions): string {
  const { format, includeMetadata = true, prettyFormat = true } = options;

  try {
    let exportData: string;

    switch (format) {
      case "json":
        if (includeMetadata) {
          const metadata = {
            exportDate: new Date().toISOString(),
            totalDiscs: discs.length,
            version: "1.0",
            source: "Disc Golf Inventory Management System",
          };

          const dataWithMetadata = {
            metadata,
            discs,
          };

          exportData = JSON.stringify(dataWithMetadata, null, prettyFormat ? 2 : 0);
        } else {
          exportData = exportDiscsToJson(discs, prettyFormat ? 2 : 0);
        }
        break;

      case "csv":
        exportData = exportDiscsToCSV(discs);
        break;

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }

    return exportData;
  } catch (error) {
    console.error("Export failed:", error);
    throw new Error(`Failed to export collection: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Generate filename for export based on current date and format
 *
 * @param format - Export format
 * @param customName - Optional custom filename prefix
 * @returns Generated filename
 */
export function generateExportFilename(format: ExportFormat, customName?: string): string {
  const timestamp = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
  const prefix = customName || "disc-golf-inventory";
  return `${prefix}-${timestamp}.${format}`;
}

/**
 * Download data as file in the browser
 *
 * @param data - Data to download
 * @param options - Download options
 */
export function downloadFile(data: string, options: DownloadOptions): void {
  try {
    const blob = new Blob([data], { type: options.mimeType });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = options.filename;
    link.style.display = "none";

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Download failed:", error);
    throw new Error(`Failed to download file: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Export and download collection as file
 *
 * @param discs - Array of discs to export
 * @param options - Export configuration options
 */
export function exportAndDownload(discs: Disc[], options: ExportOptions): void {
  try {
    const data = exportCollection(discs, options);
    const filename = options.filename || generateExportFilename(options.format);

    const mimeTypes = {
      json: "application/json",
      csv: "text/csv",
    };

    downloadFile(data, {
      filename,
      mimeType: mimeTypes[options.format],
    });
  } catch (error) {
    console.error("Export and download failed:", error);
    throw error;
  }
}

// ============================================================================
// IMPORT FUNCTIONALITY
// ============================================================================

/**
 * Validate disc data using Zod schema
 *
 * @param discs - Array of disc objects to validate
 * @returns Validation result with errors
 */
function validateDiscs(discs: unknown[]): { valid: Disc[]; errors: ValidationError[] } {
  const valid: Disc[] = [];
  const errors: ValidationError[] = [];

  discs.forEach((disc, index) => {
    try {
      // Type guard to ensure disc is an object
      if (!disc || typeof disc !== "object") {
        throw new Error("Invalid disc data: not an object");
      }

      const discObj = disc as Record<string, unknown>;

      // Convert string dates back to Date objects if needed
      const processedDisc = {
        ...discObj,
        purchaseDate: discObj.purchaseDate ? new Date(discObj.purchaseDate as string) : undefined,
        createdAt: new Date(discObj.createdAt as string),
        updatedAt: new Date(discObj.updatedAt as string),
      };

      const validatedDisc = DiscSchema.parse(processedDisc);
      valid.push(validatedDisc);
    } catch (error: unknown) {
      if (error && typeof error === "object" && "errors" in error && Array.isArray(error.errors)) {
        error.errors.forEach((err: unknown) => {
          if (err && typeof err === "object" && "path" in err && "message" in err) {
            errors.push({
              index,
              field: Array.isArray(err.path) ? err.path.join(".") : undefined,
              message: typeof err.message === "string" ? err.message : "Validation error",
              value: "received" in err ? err.received : undefined,
            });
          }
        });
      } else {
        errors.push({
          index,
          message: "Invalid disc data",
          value: disc,
        });
      }
    }
  });

  return { valid, errors };
}

/**
 * Parse and validate JSON import data
 *
 * @param jsonData - JSON string containing disc data
 * @param currentDiscs - Current disc collection for comparison
 * @returns Import result with validation and summary
 */
export function importFromJson(jsonData: string, currentDiscs: Disc[] = []): ImportResult {
  try {
    // Parse the JSON first
    const parsed = JSON.parse(jsonData);
    let discsToImport: unknown[];

    // Check if data has metadata wrapper
    if (parsed.metadata && parsed.discs && Array.isArray(parsed.discs)) {
      discsToImport = parsed.discs;
    } else if (Array.isArray(parsed)) {
      discsToImport = parsed;
    } else {
      return {
        success: false,
        error: "Data must be an array of discs or an object with a 'discs' array",
      };
    }

    // Basic validation - check if objects have required disc properties
    const requiredFields = [
      "id",
      "manufacturer",
      "mold",
      "plasticType",
      "weight",
      "condition",
      "flightNumbers",
      "color",
      "currentLocation",
    ];

    for (const item of discsToImport) {
      if (typeof item !== "object" || item === null) {
        return { success: false, error: "Invalid disc object found" };
      }

      for (const field of requiredFields) {
        if (!(field in item)) {
          return { success: false, error: `Missing required field: ${field}` };
        }
      }
    }

    // Validate all discs using Zod schema
    const { valid: validDiscs, errors: validationErrors } = validateDiscs(discsToImport);

    // Create import summary
    const summary = createImportSummary(currentDiscs, validDiscs);
    const importSummary: ImportSummary = {
      totalImported: validDiscs.length,
      totalSkipped: discsToImport.length - validDiscs.length,
      totalErrors: validationErrors.length,
      newDiscs: summary.newDiscIds.length,
      duplicates: summary.duplicateIds.length,
      replacements: summary.wouldReplace ? summary.duplicateIds.length : 0,
    };

    return {
      success: true,
      data: validDiscs,
      summary: importSummary,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
    };
  } catch (error) {
    return {
      success: false,
      error: `Import failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Read file content as text
 *
 * @param file - File object to read
 * @returns Promise resolving to file content
 */
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === "string") {
        resolve(result);
      } else {
        reject(new Error("Failed to read file as text"));
      }
    };

    reader.onerror = () => {
      reject(new Error("File reading failed"));
    };

    reader.readAsText(file);
  });
}

/**
 * Import collection from file
 *
 * @param file - File object containing disc data
 * @param currentDiscs - Current disc collection for comparison
 * @returns Promise resolving to import result
 */
export async function importFromFile(file: File, currentDiscs: Disc[] = []): Promise<ImportResult> {
  try {
    // Validate file type
    if (!file.name.toLowerCase().endsWith(".json")) {
      return {
        success: false,
        error: "Only JSON files are supported for import",
      };
    }

    // Read file content
    const content = await readFileAsText(file);

    // Import from JSON content
    return importFromJson(content, currentDiscs);
  } catch (error) {
    return {
      success: false,
      error: `File import failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}
