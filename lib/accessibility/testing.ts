/**
 * Accessibility Testing Utilities for Disc Golf Inventory Management System
 *
 * This module provides comprehensive accessibility testing utilities including
 * automated testing with axe-core, manual testing helpers, and WCAG compliance checks.
 */

import { configureAxe } from "jest-axe";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface AccessibilityTestConfig {
  /** WCAG level to test against */
  level?: "A" | "AA" | "AAA";
  /** Specific rules to include */
  rules?: string[];
  /** Rules to disable */
  disableRules?: string[];
  /** Tags to include in testing */
  tags?: string[];
  /** Whether to include experimental rules */
  experimental?: boolean;
}

export interface AccessibilityViolation {
  id: string;
  impact: "minor" | "moderate" | "serious" | "critical";
  description: string;
  help: string;
  helpUrl: string;
  nodes: Array<{
    target: string[];
    html: string;
    failureSummary: string;
  }>;
}

export interface AccessibilityTestResult {
  violations: AccessibilityViolation[];
  passes: number;
  incomplete: number;
  inapplicable: number;
  url?: string;
  timestamp: number;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

/**
 * Default axe configuration for WCAG 2.1 AA compliance
 */
export const defaultAxeConfig = {
  rules: {
    // Color contrast
    "color-contrast": { enabled: true },
    "color-contrast-enhanced": { enabled: false }, // AAA level

    // Keyboard navigation
    "focus-order-semantics": { enabled: true },
    tabindex: { enabled: true },

    // Screen reader support
    "aria-allowed-attr": { enabled: true },
    "aria-required-attr": { enabled: true },
    "aria-valid-attr": { enabled: true },
    "aria-valid-attr-value": { enabled: true },

    // Semantic HTML
    "heading-order": { enabled: true },
    "landmark-one-main": { enabled: true },
    "page-has-heading-one": { enabled: true },

    // Form accessibility
    label: { enabled: true },
    "form-field-multiple-labels": { enabled: true },

    // Image accessibility
    "image-alt": { enabled: true },
    "image-redundant-alt": { enabled: true },

    // Link accessibility
    "link-name": { enabled: true },
    "link-in-text-block": { enabled: true },
  },
  tags: ["wcag2a", "wcag2aa", "wcag21aa"],
};

/**
 * Enhanced axe configuration for comprehensive testing
 */
export const enhancedAxeConfig = {
  ...defaultAxeConfig,
  rules: {
    ...defaultAxeConfig.rules,
    // Additional AAA level rules
    "color-contrast-enhanced": { enabled: true },
    "focus-order-semantics": { enabled: true },
    "scrollable-region-focusable": { enabled: true },
  },
  tags: [...defaultAxeConfig.tags, "wcag2aaa", "best-practice"],
};

// ============================================================================
// TESTING UTILITIES
// ============================================================================

/**
 * Configure axe for testing with custom options
 */
export function createAxeConfig(config: AccessibilityTestConfig = {}) {
  const { level = "AA", rules = [], disableRules = [], tags = [], experimental = false } = config;

  const baseConfig = level === "AAA" ? enhancedAxeConfig : defaultAxeConfig;

  const axeConfig = {
    ...baseConfig,
    rules: {
      ...baseConfig.rules,
      // Enable specific rules
      ...rules.reduce((acc, rule) => ({ ...acc, [rule]: { enabled: true } }), {}),
      // Disable specific rules
      ...disableRules.reduce((acc, rule) => ({ ...acc, [rule]: { enabled: false } }), {}),
    },
    tags: tags.length > 0 ? tags : baseConfig.tags,
  };

  if (experimental) {
    axeConfig.tags.push("experimental");
  }

  return configureAxe(axeConfig);
}

/**
 * Test element for accessibility violations
 */
export async function testElementAccessibility(
  element: HTMLElement,
  config: AccessibilityTestConfig = {}
): Promise<AccessibilityTestResult> {
  const axe = createAxeConfig(config);

  try {
    const results = await axe(element);

    return {
      violations: results.violations.map((violation: unknown) => {
        const v = violation as {
          id: string;
          impact: string;
          description: string;
          help: string;
          helpUrl: string;
          nodes: Array<{
            target: string[];
            html: string;
            failureSummary: string;
          }>;
        };
        return {
          id: v.id,
          impact: v.impact as "minor" | "moderate" | "serious" | "critical",
          description: v.description,
          help: v.help,
          helpUrl: v.helpUrl,
          nodes: v.nodes.map((node) => ({
            target: node.target,
            html: node.html,
            failureSummary: node.failureSummary,
          })),
        };
      }),
      passes: results.passes.length,
      incomplete: results.incomplete.length,
      inapplicable: results.inapplicable.length,
      timestamp: Date.now(),
    };
  } catch (error) {
    console.error("Accessibility testing failed:", error);
    throw error;
  }
}

/**
 * Generate accessibility report
 */
export function generateAccessibilityReport(
  results: AccessibilityTestResult[],
  options: { format?: "json" | "html" | "markdown" } = {}
): string {
  const { format = "markdown" } = options;

  const totalViolations = results.reduce((sum, result) => sum + result.violations.length, 0);
  const criticalViolations = results.reduce(
    (sum, result) => sum + result.violations.filter((v) => v.impact === "critical").length,
    0
  );
  const seriousViolations = results.reduce(
    (sum, result) => sum + result.violations.filter((v) => v.impact === "serious").length,
    0
  );

  if (format === "json") {
    return JSON.stringify(
      {
        summary: {
          totalTests: results.length,
          totalViolations,
          criticalViolations,
          seriousViolations,
        },
        results,
      },
      null,
      2
    );
  }

  if (format === "markdown") {
    let report = "# Accessibility Test Report\n\n";
    report += `**Generated:** ${new Date().toISOString()}\n\n`;
    report += "## Summary\n\n";
    report += `- **Total Tests:** ${results.length}\n`;
    report += `- **Total Violations:** ${totalViolations}\n`;
    report += `- **Critical Violations:** ${criticalViolations}\n`;
    report += `- **Serious Violations:** ${seriousViolations}\n\n`;

    if (totalViolations > 0) {
      report += "## Violations\n\n";
      results.forEach((result, index) => {
        if (result.violations.length > 0) {
          report += `### Test ${index + 1}\n\n`;
          result.violations.forEach((violation) => {
            report += `#### ${violation.id} (${violation.impact})\n\n`;
            report += `${violation.description}\n\n`;
            report += `**Help:** ${violation.help}\n\n`;
            report += `**More Info:** [${violation.helpUrl}](${violation.helpUrl})\n\n`;
          });
        }
      });
    }

    return report;
  }

  return JSON.stringify(results, null, 2);
}

// ============================================================================
// MANUAL TESTING HELPERS
// ============================================================================

/**
 * Check if element is keyboard accessible
 */
export function isKeyboardAccessible(element: HTMLElement): boolean {
  const tabIndex = element.tabIndex;
  const tagName = element.tagName.toLowerCase();

  // Interactive elements that should be focusable
  const interactiveElements = ["button", "input", "select", "textarea", "a"];

  if (interactiveElements.includes(tagName)) {
    return tabIndex >= 0;
  }

  // Elements with explicit tabindex
  return tabIndex >= 0;
}

/**
 * Check color contrast ratio
 */
export function checkColorContrast(
  foreground: string,
  background: string,
  fontSize: number = 16
): { ratio: number; passes: { aa: boolean; aaa: boolean } } {
  // This is a simplified implementation
  // In a real application, you would use a proper color contrast library
  const ratio = 4.5; // Placeholder - implement actual contrast calculation

  const largeText = fontSize >= 18 || fontSize >= 14; // Bold text

  return {
    ratio,
    passes: {
      aa: largeText ? ratio >= 3 : ratio >= 4.5,
      aaa: largeText ? ratio >= 4.5 : ratio >= 7,
    },
  };
}

const accessibilityTesting = {
  createAxeConfig,
  testElementAccessibility,
  generateAccessibilityReport,
  isKeyboardAccessible,
  checkColorContrast,
};

export default accessibilityTesting;
