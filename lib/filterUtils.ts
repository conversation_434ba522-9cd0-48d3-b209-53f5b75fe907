/**
 * Filter Utilities for the Disc Golf Inventory Management System
 *
 * This module provides advanced filtering functionality for disc collections including
 * text search, range filtering, multi-select filtering, and efficient algorithms.
 * Builds upon the existing discUtils.ts search functions with enhanced capabilities.
 */

import type { Disc, DiscFilterCriteria, FlightNumbers } from "./types";
import { DiscCondition, Location } from "./types";
import { filterDiscs as baseFilterDiscs } from "./discUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Enhanced filter criteria with additional options
 */
export interface EnhancedFilterCriteria extends DiscFilterCriteria {
  // Range filters
  weightRange?: { min: number; max: number };
  speedRange?: { min: number; max: number };
  glideRange?: { min: number; max: number };
  turnRange?: { min: number; max: number };
  fadeRange?: { min: number; max: number };

  // Date range filters
  purchaseDateRange?: { start: Date; end: Date };
  createdDateRange?: { start: Date; end: Date };

  // Multi-select filters
  manufacturers?: string[];
  conditions?: DiscCondition[];
  locations?: Location[];
  colors?: string[];
  plasticTypes?: string[];

  // Search options
  searchFields?: (keyof Disc)[];
  fuzzySearch?: boolean;
  caseSensitive?: boolean;
}

/**
 * Search configuration options
 */
export interface SearchOptions {
  /** Fields to search across */
  fields?: (keyof Disc)[];
  /** Enable fuzzy matching */
  fuzzy?: boolean;
  /** Case sensitive search */
  caseSensitive?: boolean;
  /** Minimum word length for search */
  minWordLength?: number;
  /** Enable word-based search (vs substring) */
  wordBased?: boolean;
}

/**
 * Filter performance metrics
 */
export interface FilterMetrics {
  totalDiscs: number;
  filteredDiscs: number;
  executionTime: number;
  filtersApplied: string[];
}

// ============================================================================
// ENHANCED TEXT SEARCH UTILITIES
// ============================================================================

/**
 * Enhanced text search with configurable options
 *
 * @param discs - Array of discs to search
 * @param query - Search query string
 * @param options - Search configuration options
 * @returns Filtered array of discs matching the query
 */
export function enhancedSearchDiscs(discs: Disc[], query: string, options: SearchOptions = {}): Disc[] {
  if (!query.trim()) {
    return discs;
  }

  const {
    fields = ["manufacturer", "mold", "plasticType", "color", "notes"],
    fuzzy = false,
    caseSensitive = false,
    minWordLength = 2,
    wordBased = false,
  } = options;

  const searchTerm = caseSensitive ? query.trim() : query.toLowerCase().trim();
  const searchWords = wordBased ? searchTerm.split(/\s+/).filter((word) => word.length >= minWordLength) : [searchTerm];

  return discs.filter((disc) => {
    const searchableText = fields
      .map((field) => {
        const value = disc[field];
        if (value === null || value === undefined) return "";

        // Handle different field types
        if (typeof value === "string") return value;
        if (typeof value === "number") return value.toString();
        if (value instanceof Date) return value.toISOString();
        if (typeof value === "object" && "speed" in value) {
          // Handle FlightNumbers
          const flight = value as FlightNumbers;
          return `${flight.speed}|${flight.glide}|${flight.turn}|${flight.fade}`;
        }
        return String(value);
      })
      .join(" ");

    const textToSearch = caseSensitive ? searchableText : searchableText.toLowerCase();

    if (wordBased) {
      return searchWords.every((word) => (fuzzy ? fuzzyMatch(textToSearch, word) : textToSearch.includes(word)));
    } else {
      return fuzzy ? fuzzyMatch(textToSearch, searchTerm) : textToSearch.includes(searchTerm);
    }
  });
}

/**
 * Simple fuzzy matching algorithm
 *
 * @param text - Text to search in
 * @param pattern - Pattern to search for
 * @returns True if pattern fuzzy matches text
 */
function fuzzyMatch(text: string, pattern: string): boolean {
  const textChars = text.split("");
  const patternChars = pattern.split("");
  let textIndex = 0;
  let patternIndex = 0;

  while (textIndex < textChars.length && patternIndex < patternChars.length) {
    if (textChars[textIndex] === patternChars[patternIndex]) {
      patternIndex++;
    }
    textIndex++;
  }

  return patternIndex === patternChars.length;
}

// ============================================================================
// ADVANCED RANGE FILTERING
// ============================================================================

/**
 * Filter discs by flight number ranges
 *
 * @param discs - Array of discs to filter
 * @param ranges - Flight number range criteria
 * @returns Filtered array of discs
 */
export function filterByFlightRanges(
  discs: Disc[],
  ranges: {
    speed?: { min: number; max: number };
    glide?: { min: number; max: number };
    turn?: { min: number; max: number };
    fade?: { min: number; max: number };
  }
): Disc[] {
  return discs.filter((disc) => {
    const { speed, glide, turn, fade } = disc.flightNumbers;

    if (ranges.speed && (speed < ranges.speed.min || speed > ranges.speed.max)) {
      return false;
    }
    if (ranges.glide && (glide < ranges.glide.min || glide > ranges.glide.max)) {
      return false;
    }
    if (ranges.turn && (turn < ranges.turn.min || turn > ranges.turn.max)) {
      return false;
    }
    if (ranges.fade && (fade < ranges.fade.min || fade > ranges.fade.max)) {
      return false;
    }

    return true;
  });
}

/**
 * Filter discs by date ranges
 *
 * @param discs - Array of discs to filter
 * @param dateRanges - Date range criteria
 * @returns Filtered array of discs
 */
export function filterByDateRanges(
  discs: Disc[],
  dateRanges: {
    purchaseDate?: { start: Date; end: Date };
    createdDate?: { start: Date; end: Date };
  }
): Disc[] {
  return discs.filter((disc) => {
    if (dateRanges.purchaseDate && disc.purchaseDate) {
      const purchaseDate = new Date(disc.purchaseDate);
      if (purchaseDate < dateRanges.purchaseDate.start || purchaseDate > dateRanges.purchaseDate.end) {
        return false;
      }
    }

    if (dateRanges.createdDate) {
      const createdDate = new Date(disc.createdAt);
      if (createdDate < dateRanges.createdDate.start || createdDate > dateRanges.createdDate.end) {
        return false;
      }
    }

    return true;
  });
}

// ============================================================================
// MULTI-SELECT FILTERING
// ============================================================================

/**
 * Filter discs by multiple selection criteria
 *
 * @param discs - Array of discs to filter
 * @param multiSelectFilters - Multi-select filter criteria
 * @returns Filtered array of discs
 */
export function filterByMultiSelect(
  discs: Disc[],
  multiSelectFilters: {
    manufacturers?: string[];
    conditions?: DiscCondition[];
    locations?: Location[];
    colors?: string[];
    plasticTypes?: string[];
  }
): Disc[] {
  return discs.filter((disc) => {
    // Filter by manufacturers
    if (multiSelectFilters.manufacturers && multiSelectFilters.manufacturers.length > 0) {
      if (!multiSelectFilters.manufacturers.includes(disc.manufacturer)) {
        return false;
      }
    }

    // Filter by conditions
    if (multiSelectFilters.conditions && multiSelectFilters.conditions.length > 0) {
      if (!multiSelectFilters.conditions.includes(disc.condition)) {
        return false;
      }
    }

    // Filter by locations
    if (multiSelectFilters.locations && multiSelectFilters.locations.length > 0) {
      if (!multiSelectFilters.locations.includes(disc.currentLocation)) {
        return false;
      }
    }

    // Filter by colors
    if (multiSelectFilters.colors && multiSelectFilters.colors.length > 0) {
      if (!multiSelectFilters.colors.includes(disc.color)) {
        return false;
      }
    }

    // Filter by plastic types
    if (multiSelectFilters.plasticTypes && multiSelectFilters.plasticTypes.length > 0) {
      if (!multiSelectFilters.plasticTypes.includes(disc.plasticType)) {
        return false;
      }
    }

    return true;
  });
}

// ============================================================================
// COMPREHENSIVE FILTER COMBINATION
// ============================================================================

/**
 * Apply all filter criteria to a disc collection with performance tracking
 *
 * @param discs - Array of discs to filter
 * @param criteria - Enhanced filter criteria
 * @returns Object containing filtered discs and performance metrics
 */
export function applyAllFilters(
  discs: Disc[],
  criteria: EnhancedFilterCriteria
): { discs: Disc[]; metrics: FilterMetrics } {
  const startTime = performance.now();
  const filtersApplied: string[] = [];
  let filteredDiscs = [...discs];

  // Apply text search first (most selective)
  if (criteria.searchTerm) {
    const searchOptions: SearchOptions = {
      fields: criteria.searchFields,
      fuzzy: criteria.fuzzySearch,
      caseSensitive: criteria.caseSensitive,
    };
    filteredDiscs = enhancedSearchDiscs(filteredDiscs, criteria.searchTerm, searchOptions);
    filtersApplied.push("textSearch");
  }

  // Apply multi-select filters
  const multiSelectFilters = {
    manufacturers: criteria.manufacturers,
    conditions: criteria.conditions,
    locations: criteria.locations,
    colors: criteria.colors,
    plasticTypes: criteria.plasticTypes,
  };

  if (Object.values(multiSelectFilters).some((filter) => filter && filter.length > 0)) {
    filteredDiscs = filterByMultiSelect(filteredDiscs, multiSelectFilters);
    filtersApplied.push("multiSelect");
  }

  // Apply range filters
  const rangeFilters = {
    speed: criteria.speedRange,
    glide: criteria.glideRange,
    turn: criteria.turnRange,
    fade: criteria.fadeRange,
  };

  if (Object.values(rangeFilters).some((filter) => filter)) {
    filteredDiscs = filterByFlightRanges(filteredDiscs, rangeFilters);
    filtersApplied.push("flightRanges");
  }

  // Apply weight range filter
  if (criteria.weightRange) {
    filteredDiscs = filteredDiscs.filter(
      (disc) => disc.weight >= criteria.weightRange!.min && disc.weight <= criteria.weightRange!.max
    );
    filtersApplied.push("weightRange");
  }

  // Apply date range filters
  const dateRanges = {
    purchaseDate: criteria.purchaseDateRange,
    createdDate: criteria.createdDateRange,
  };

  if (Object.values(dateRanges).some((filter) => filter)) {
    filteredDiscs = filterByDateRanges(filteredDiscs, dateRanges);
    filtersApplied.push("dateRanges");
  }

  // Apply legacy filter criteria for backward compatibility
  if (
    criteria.manufacturer ||
    criteria.mold ||
    criteria.plasticType ||
    criteria.condition ||
    criteria.location ||
    criteria.color ||
    criteria.minWeight ||
    criteria.maxWeight ||
    criteria.minSpeed ||
    criteria.maxSpeed
  ) {
    const legacyFilters = {
      manufacturers: criteria.manufacturer ? [criteria.manufacturer] : undefined,
      conditions: criteria.condition ? [criteria.condition] : undefined,
      locations: criteria.location ? [criteria.location] : undefined,
      weightRange:
        criteria.minWeight || criteria.maxWeight
          ? {
              min: criteria.minWeight ?? 0,
              max: criteria.maxWeight ?? 300,
            }
          : undefined,
      speedRange:
        criteria.minSpeed || criteria.maxSpeed
          ? {
              min: criteria.minSpeed ?? 0,
              max: criteria.maxSpeed ?? 20,
            }
          : undefined,
    };

    filteredDiscs = baseFilterDiscs(filteredDiscs, legacyFilters);
    filtersApplied.push("legacy");
  }

  const endTime = performance.now();

  const metrics: FilterMetrics = {
    totalDiscs: discs.length,
    filteredDiscs: filteredDiscs.length,
    executionTime: endTime - startTime,
    filtersApplied,
  };

  return { discs: filteredDiscs, metrics };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get unique values for a specific field across all discs
 *
 * @param discs - Array of discs
 * @param field - Field to extract unique values from
 * @returns Array of unique values
 */
export function getUniqueFieldValues<K extends keyof Disc>(discs: Disc[], field: K): Array<Disc[K]> {
  const values = discs.map((disc) => disc[field]).filter((value) => value !== null && value !== undefined);
  return Array.from(new Set(values));
}

/**
 * Get filter suggestions based on current disc collection
 *
 * @param discs - Array of discs
 * @returns Object containing filter suggestions
 */
export function getFilterSuggestions(discs: Disc[]) {
  if (discs.length === 0) {
    return {
      manufacturers: [],
      plasticTypes: [],
      colors: [],
      conditions: Object.values(DiscCondition),
      locations: Object.values(Location),
      weightRange: { min: 150, max: 180 },
      speedRange: { min: 1, max: 14 },
    };
  }

  return {
    manufacturers: getUniqueFieldValues(discs, "manufacturer").sort(),
    plasticTypes: getUniqueFieldValues(discs, "plasticType").sort(),
    colors: getUniqueFieldValues(discs, "color").sort(),
    conditions: Object.values(DiscCondition),
    locations: Object.values(Location),
    weightRange: {
      min: Math.min(...discs.map((d) => d.weight)),
      max: Math.max(...discs.map((d) => d.weight)),
    },
    speedRange: {
      min: Math.min(...discs.map((d) => d.flightNumbers.speed)),
      max: Math.max(...discs.map((d) => d.flightNumbers.speed)),
    },
  };
}

/**
 * Create an empty filter criteria object
 *
 * @returns Empty enhanced filter criteria
 */
export function createEmptyFilterCriteria(): EnhancedFilterCriteria {
  return {};
}

/**
 * Check if any filters are currently active
 *
 * @param criteria - Filter criteria to check
 * @returns True if any filters are active
 */
export function hasActiveFilters(criteria: EnhancedFilterCriteria): boolean {
  return !!(
    criteria.searchTerm ||
    criteria.manufacturer ||
    criteria.mold ||
    criteria.plasticType ||
    criteria.condition ||
    criteria.location ||
    criteria.color ||
    criteria.minWeight ||
    criteria.maxWeight ||
    criteria.minSpeed ||
    criteria.maxSpeed ||
    criteria.weightRange ||
    criteria.speedRange ||
    criteria.glideRange ||
    criteria.turnRange ||
    criteria.fadeRange ||
    criteria.purchaseDateRange ||
    criteria.createdDateRange ||
    (criteria.manufacturers && criteria.manufacturers.length > 0) ||
    (criteria.conditions && criteria.conditions.length > 0) ||
    (criteria.locations && criteria.locations.length > 0) ||
    (criteria.colors && criteria.colors.length > 0) ||
    (criteria.plasticTypes && criteria.plasticTypes.length > 0)
  );
}

/**
 * Clear all active filters
 *
 * @param _criteria - Current filter criteria (unused)
 * @returns Empty filter criteria
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function clearAllFilters(_criteria: EnhancedFilterCriteria): EnhancedFilterCriteria {
  return createEmptyFilterCriteria();
}

/**
 * Serialize filter criteria to URL search parameters
 *
 * @param criteria - Filter criteria to serialize
 * @returns URLSearchParams object
 */
export function serializeFiltersToURL(criteria: EnhancedFilterCriteria): URLSearchParams {
  const params = new URLSearchParams();

  if (criteria.searchTerm) params.set("q", criteria.searchTerm);
  if (criteria.manufacturers?.length) params.set("manufacturers", criteria.manufacturers.join(","));
  if (criteria.conditions?.length) params.set("conditions", criteria.conditions.join(","));
  if (criteria.locations?.length) params.set("locations", criteria.locations.join(","));
  if (criteria.colors?.length) params.set("colors", criteria.colors.join(","));
  if (criteria.plasticTypes?.length) params.set("plasticTypes", criteria.plasticTypes.join(","));

  if (criteria.weightRange) {
    params.set("weightMin", criteria.weightRange.min.toString());
    params.set("weightMax", criteria.weightRange.max.toString());
  }

  if (criteria.speedRange) {
    params.set("speedMin", criteria.speedRange.min.toString());
    params.set("speedMax", criteria.speedRange.max.toString());
  }

  return params;
}

/**
 * Deserialize filter criteria from URL search parameters
 *
 * @param params - URLSearchParams to deserialize
 * @returns Enhanced filter criteria
 */
export function deserializeFiltersFromURL(params: URLSearchParams): EnhancedFilterCriteria {
  const criteria: EnhancedFilterCriteria = {};

  const searchTerm = params.get("q");
  if (searchTerm) criteria.searchTerm = searchTerm;

  const manufacturers = params.get("manufacturers");
  if (manufacturers) criteria.manufacturers = manufacturers.split(",");

  const conditions = params.get("conditions");
  if (conditions) criteria.conditions = conditions.split(",") as DiscCondition[];

  const locations = params.get("locations");
  if (locations) criteria.locations = locations.split(",") as Location[];

  const colors = params.get("colors");
  if (colors) criteria.colors = colors.split(",");

  const plasticTypes = params.get("plasticTypes");
  if (plasticTypes) criteria.plasticTypes = plasticTypes.split(",");

  const weightMin = params.get("weightMin");
  const weightMax = params.get("weightMax");
  if (weightMin && weightMax) {
    criteria.weightRange = {
      min: parseInt(weightMin, 10),
      max: parseInt(weightMax, 10),
    };
  }

  const speedMin = params.get("speedMin");
  const speedMax = params.get("speedMax");
  if (speedMin && speedMax) {
    criteria.speedRange = {
      min: parseInt(speedMin, 10),
      max: parseInt(speedMax, 10),
    };
  }

  return criteria;
}
