import "@testing-library/jest-dom";
import { configureAxe, toHaveNoViolations } from "jest-axe";
import { vi, expect } from "vitest";

// Configure jest-axe for accessibility testing
const axe = configureAxe({
  rules: {
    // Disable color-contrast rule for testing (can be flaky in jsdom)
    "color-contrast": { enabled: false },
  },
});

// Make axe available globally for tests
(global as any).axe = axe;

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

// Mock performance.now for testing
global.performance =
  global.performance ||
  ({
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
    clearMarks: () => {},
    clearMeasures: () => {},
  } as any);

// Suppress React 19 warnings in tests
const originalError = console.error;
console.error = (...args: any[]) => {
  if (
    typeof args[0] === "string" &&
    (args[0].includes("Maximum update depth exceeded") ||
      args[0].includes("Warning: An update to") ||
      args[0].includes("act(...)"))
  ) {
    return;
  }
  originalError.call(console, ...args);
};
